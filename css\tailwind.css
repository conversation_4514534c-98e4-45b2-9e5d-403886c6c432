@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary: #1e40af; /* blue-800 - Deep blue for academic authority */
  --color-primary-50: #eff6ff; /* blue-50 */
  --color-primary-100: #dbeafe; /* blue-100 */
  --color-primary-500: #3b82f6; /* blue-500 */
  --color-primary-600: #2563eb; /* blue-600 */
  --color-primary-700: #1d4ed8; /* blue-700 */
  --color-primary-800: #1e40af; /* blue-800 */
  --color-primary-900: #1e3a8a; /* blue-900 */

  /* Secondary Colors */
  --color-secondary: #64748b; /* slate-500 - Professional neutrality */
  --color-secondary-50: #f8fafc; /* slate-50 */
  --color-secondary-100: #f1f5f9; /* slate-100 */
  --color-secondary-200: #e2e8f0; /* slate-200 */
  --color-secondary-300: #cbd5e1; /* slate-300 */
  --color-secondary-400: #94a3b8; /* slate-400 */
  --color-secondary-500: #64748b; /* slate-500 */
  --color-secondary-600: #475569; /* slate-600 */
  --color-secondary-700: #334155; /* slate-700 */
  --color-secondary-800: #1e293b; /* slate-800 */
  --color-secondary-900: #0f172a; /* slate-900 */

  /* Accent Colors */
  --color-accent: #0ea5e9; /* sky-500 - Interactive elements */
  --color-accent-50: #f0f9ff; /* sky-50 */
  --color-accent-100: #e0f2fe; /* sky-100 */
  --color-accent-500: #0ea5e9; /* sky-500 */
  --color-accent-600: #0284c7; /* sky-600 */

  /* Background Colors */
  --color-background: #f8fafc; /* slate-50 - Soft off-white */
  --color-surface: #ffffff; /* white - Pure white for content */

  /* Text Colors */
  --color-text-primary: #0f172a; /* slate-900 - Near-black for readability */
  --color-text-secondary: #475569; /* slate-600 - Medium gray for hierarchy */

  /* Status Colors */
  --color-success: #059669; /* emerald-600 - Professional green */
  --color-success-50: #ecfdf5; /* emerald-50 */
  --color-success-100: #d1fae5; /* emerald-100 */
  --color-success-500: #10b981; /* emerald-500 */
  --color-success-600: #059669; /* emerald-600 */

  --color-warning: #d97706; /* amber-600 - Caution indicator */
  --color-warning-50: #fffbeb; /* amber-50 */
  --color-warning-100: #fef3c7; /* amber-100 */
  --color-warning-500: #f59e0b; /* amber-500 */
  --color-warning-600: #d97706; /* amber-600 */

  --color-error: #dc2626; /* red-600 - Critical issues */
  --color-error-50: #fef2f2; /* red-50 */
  --color-error-100: #fee2e2; /* red-100 */
  --color-error-500: #ef4444; /* red-500 */
  --color-error-600: #dc2626; /* red-600 */

  /* Shadow Variables */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* Border Variables */
  --border-light: #e2e8f0; /* slate-200 */
  --border-medium: #cbd5e1; /* slate-300 */

  /* Animation Variables */
  --transition-fast: 200ms ease-out;
  --transition-normal: 300ms ease-out;
}

/* Base Styles */
@layer base {
  body {
    font-family: 'Source Sans Pro', sans-serif;
    color: var(--color-text-primary);
    background-color: var(--color-background);
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    line-height: 1.3;
  }

  /* Focus styles for accessibility */
  *:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Component Styles */
@layer components {
  .btn-primary {
    @apply bg-primary text-white px-4 py-2 rounded-md font-medium transition-all duration-200 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-700 px-4 py-2 rounded-md font-medium transition-all duration-200 hover:bg-secondary-200 focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-surface rounded-lg shadow-sm border border-secondary-200 p-6;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-secondary-200 rounded-md focus:border-accent focus:ring-1 focus:ring-accent transition-colors duration-200;
  }

  .text-caption {
    font-family: 'Inter', sans-serif;
    @apply text-sm text-secondary-600;
  }

  .text-data {
    font-family: 'JetBrains Mono', monospace;
    @apply text-sm;
  }
.status-success  {
    @apply bg-success-50 border;
  }
.status-warning  {
    @apply bg-warning-50 border;
  }
.status-error  {
    @apply bg-error-50 border;
  }
}

/* Utility Classes */
@layer utilities {
  .shadow-subtle {
    box-shadow: var(--shadow-sm);
  }

  .shadow-elevated {
    box-shadow: var(--shadow-md);
  }

  .shadow-modal {
    box-shadow: var(--shadow-lg);
  }

  .transition-fast {
    transition: all var(--transition-fast);
  }

  .transition-normal {
    transition: all var(--transition-normal);
  }

  .border-light {
    border-color: var(--border-light);
  }

  .border-medium {
    border-color: var(--border-medium);
  }
}