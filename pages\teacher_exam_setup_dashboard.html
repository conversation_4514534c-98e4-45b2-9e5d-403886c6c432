<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teacher Exam Setup Dashboard - Online Exam Platform</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fonlineexa5436back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface shadow-subtle border-b border-light sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L3 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-13-5z"/>
                            <path d="M14 14h4v2h-4v-2zm0-4h4v2h-4v-2zm0 8h4v2h-4v-2z" fill="white"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-text-primary">ExamPro</h1>
                        <p class="text-sm text-text-secondary">Online Exam Platform</p>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="teacher_exam_setup_dashboard.html" class="bg-primary-50 text-primary px-3 py-2 rounded-md text-sm font-medium border border-primary-200">
                        <i class="fas fa-plus-circle mr-2"></i>Create Exam
                    </a>
                    <a href="teacher_results_dashboard.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>Results
                    </a>
                    <a href="student_exam_login.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-user-graduate mr-2"></i>Student Portal
                    </a>
                </nav>

                <!-- Language Toggle & User Menu -->
                <div class="flex items-center space-x-4">
                    <button id="languageToggle" class="text-text-secondary hover:text-primary transition-colors">
                        <i class="fas fa-globe mr-1"></i>
                        <span id="currentLang">EN</span>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="Teacher Profile" class="w-8 h-8 rounded-full object-cover" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <span class="text-sm text-text-secondary">Dr. Ahmed</span>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn" class="text-text-secondary hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="md:hidden hidden bg-surface border-t border-light">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="teacher_exam_setup_dashboard.html" class="bg-primary-50 text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-plus-circle mr-2"></i>Create Exam
                </a>
                <a href="teacher_results_dashboard.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-chart-bar mr-2"></i>Results
                </a>
                <a href="student_exam_login.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-user-graduate mr-2"></i>Student Portal
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-4 lg:gap-8">
            <!-- Main Form Area -->
            <div class="lg:col-span-3 space-y-8">
                <!-- Page Header -->
                <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-semibold text-text-primary">Create New Exam</h2>
                            <p class="text-text-secondary mt-1">Set up your online examination with questions and configurations</p>
                        </div>
                        <div class="flex items-center space-x-2 text-sm text-text-secondary">
                            <i class="fas fa-calendar-alt"></i>
                            <span>July 17, 2025</span>
                        </div>
                    </div>
                </div>

                <!-- Exam Metadata Section -->
                <section class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex items-center mb-6">
                        <div class="bg-primary-50 p-2 rounded-lg">
                            <i class="fas fa-info-circle text-primary text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-text-primary">Exam Information</h3>
                            <p class="text-sm text-text-secondary">Basic details about your examination</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="college" class="block text-sm font-medium text-text-primary mb-2">
                                College/Department <span class="text-error">*</span>
                            </label>
                            <input type="text" id="college" name="college" class="form-input" placeholder="e.g., Faculty of Computer Science" value="Faculty of Computer Science" />
                        </div>
                        <div>
                            <label for="major" class="block text-sm font-medium text-text-primary mb-2">
                                Major <span class="text-error">*</span>
                            </label>
                            <input type="text" id="major" name="major" class="form-input" placeholder="e.g., Software Engineering" value="Software Engineering" />
                        </div>
                        <div>
                            <label for="courseName" class="block text-sm font-medium text-text-primary mb-2">
                                Course Name <span class="text-error">*</span>
                            </label>
                            <input type="text" id="courseName" name="courseName" class="form-input" placeholder="e.g., Database Systems" value="Database Systems" />
                        </div>
                        <div>
                            <label for="examTitle" class="block text-sm font-medium text-text-primary mb-2">
                                Exam Title <span class="text-error">*</span>
                            </label>
                            <input type="text" id="examTitle" name="examTitle" class="form-input" placeholder="e.g., Midterm Exam - Chapter 1-5" value="Midterm Exam - Chapter 1-5" />
                        </div>
                    </div>
                </section>

                <!-- Student Management Section -->
                <section class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex items-center mb-6">
                        <div class="bg-accent-50 p-2 rounded-lg">
                            <i class="fas fa-users text-accent text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-text-primary">Student Management</h3>
                            <p class="text-sm text-text-secondary">Add students manually or import from CSV file</p>
                        </div>
                    </div>

                    <!-- Manual Student Entry -->
                    <div class="mb-8">
                        <h4 class="text-md font-medium text-text-primary mb-4">Manual Entry</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="studentId" class="block text-sm font-medium text-text-primary mb-2">Student ID</label>
                                <input type="text" id="studentId" name="studentId" class="form-input" placeholder="e.g., ST001" />
                            </div>
                            <div>
                                <label for="studentName" class="block text-sm font-medium text-text-primary mb-2">Student Name</label>
                                <input type="text" id="studentName" name="studentName" class="form-input" placeholder="e.g., Ahmed Hassan" />
                            </div>
                            <div class="flex items-end">
                                <button id="addStudentBtn" class="btn-primary w-full">
                                    <i class="fas fa-plus mr-2"></i>Add Student
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- CSV Upload -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-text-primary mb-4">CSV Import</h4>
                        <div id="csvUploadArea" class="border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center hover:border-accent transition-colors cursor-pointer">
                            <div class="space-y-4">
                                <div class="mx-auto w-12 h-12 bg-accent-50 rounded-full flex items-center justify-center">
                                    <i class="fas fa-cloud-upload-alt text-accent text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-text-primary font-medium">Drop your CSV file here or click to browse</p>
                                    <p class="text-sm text-text-secondary mt-1">CSV should contain StudentID and StudentName columns</p>
                                </div>
                                <input type="file" id="csvFileInput" accept=".csv" class="hidden" />
                            </div>
                        </div>
                        <div id="csvFeedback" class="mt-3 hidden"></div>
                    </div>

                    <!-- Student List -->
                    <div id="studentList" class="space-y-3">
                        <h4 class="text-md font-medium text-text-primary">Enrolled Students (5)</h4>
                        <div class="bg-secondary-50 rounded-lg p-4 space-y-2">
                            <div class="flex items-center justify-between py-2 px-3 bg-surface rounded border border-light">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-mono text-text-secondary">ST001</span>
                                    <span class="text-sm text-text-primary">Ahmed Hassan</span>
                                </div>
                                <button class="text-error hover:text-error-600 transition-colors">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div class="flex items-center justify-between py-2 px-3 bg-surface rounded border border-light">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-mono text-text-secondary">ST002</span>
                                    <span class="text-sm text-text-primary">Fatima Al-Zahra</span>
                                </div>
                                <button class="text-error hover:text-error-600 transition-colors">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div class="flex items-center justify-between py-2 px-3 bg-surface rounded border border-light">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-mono text-text-secondary">ST003</span>
                                    <span class="text-sm text-text-primary">Omar Khalil</span>
                                </div>
                                <button class="text-error hover:text-error-600 transition-colors">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div class="flex items-center justify-between py-2 px-3 bg-surface rounded border border-light">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-mono text-text-secondary">ST004</span>
                                    <span class="text-sm text-text-primary">Layla Mohammed</span>
                                </div>
                                <button class="text-error hover:text-error-600 transition-colors">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div class="flex items-center justify-between py-2 px-3 bg-surface rounded border border-light">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-mono text-text-secondary">ST005</span>
                                    <span class="text-sm text-text-primary">Yusuf Ibrahim</span>
                                </div>
                                <button class="text-error hover:text-error-600 transition-colors">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Question Management Section -->
                <section class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="bg-warning-50 p-2 rounded-lg">
                                <i class="fas fa-question-circle text-warning text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-text-primary">Question Management</h3>
                                <p class="text-sm text-text-secondary">Create and configure exam questions</p>
                            </div>
                        </div>
                        <button id="addQuestionBtn" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Add Question
                        </button>
                    </div>

                    <div id="questionsList" class="space-y-6">
                        <!-- Question 1 -->
                        <div class="question-card border border-light rounded-lg p-6 bg-secondary-50">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-medium text-text-primary">Question 1</h4>
                                <div class="flex items-center space-x-2">
                                    <button class="text-text-secondary hover:text-primary transition-colors">
                                        <i class="fas fa-expand-alt"></i>
                                    </button>
                                    <button class="text-error hover:text-error-600 transition-colors">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-primary mb-2">Question Text</label>
                                    <textarea class="form-input h-24 resize-none" placeholder="Enter your question here...">What is the primary key in a relational database?</textarea>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_1" value="A" id="q1_option_a" class="text-primary" />
                                            <label for="q1_option_a" class="text-sm font-medium text-text-primary">A.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option A" value="A unique identifier for each record" />
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_1" value="B" id="q1_option_b" class="text-primary" />
                                            <label for="q1_option_b" class="text-sm font-medium text-text-primary">B.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option B" value="A foreign key reference" />
                                        </div>
                                    </div>
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_1" value="C" id="q1_option_c" class="text-primary" checked />
                                            <label for="q1_option_c" class="text-sm font-medium text-text-primary">C.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option C" value="An index for faster queries" />
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_1" value="D" id="q1_option_d" class="text-primary" />
                                            <label for="q1_option_d" class="text-sm font-medium text-text-primary">D.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option D" value="A data validation rule" />
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-4 pt-2">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="text-primary" checked />
                                        <span class="text-sm text-text-primary">Shuffle answer choices</span>
                                    </label>
                                    <span class="text-sm text-success-600 bg-success-50 px-2 py-1 rounded">
                                        <i class="fas fa-check-circle mr-1"></i>Correct answer: A
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Question 2 -->
                        <div class="question-card border border-light rounded-lg p-6 bg-secondary-50">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-medium text-text-primary">Question 2</h4>
                                <div class="flex items-center space-x-2">
                                    <button class="text-text-secondary hover:text-primary transition-colors">
                                        <i class="fas fa-expand-alt"></i>
                                    </button>
                                    <button class="text-error hover:text-error-600 transition-colors">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-primary mb-2">Question Text</label>
                                    <textarea class="form-input h-24 resize-none" placeholder="Enter your question here...">Which SQL command is used to retrieve data from a database?</textarea>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_2" value="A" id="q2_option_a" class="text-primary" checked />
                                            <label for="q2_option_a" class="text-sm font-medium text-text-primary">A.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option A" value="SELECT" />
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_2" value="B" id="q2_option_b" class="text-primary" />
                                            <label for="q2_option_b" class="text-sm font-medium text-text-primary">B.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option B" value="INSERT" />
                                        </div>
                                    </div>
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_2" value="C" id="q2_option_c" class="text-primary" />
                                            <label for="q2_option_c" class="text-sm font-medium text-text-primary">C.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option C" value="UPDATE" />
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <input type="radio" name="correct_answer_2" value="D" id="q2_option_d" class="text-primary" />
                                            <label for="q2_option_d" class="text-sm font-medium text-text-primary">D.</label>
                                            <input type="text" class="form-input flex-1" placeholder="Option D" value="DELETE" />
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-4 pt-2">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="text-primary" />
                                        <span class="text-sm text-text-primary">Shuffle answer choices</span>
                                    </label>
                                    <span class="text-sm text-success-600 bg-success-50 px-2 py-1 rounded">
                                        <i class="fas fa-check-circle mr-1"></i>Correct answer: A
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Right Sidebar - Exam Configuration -->
            <div class="lg:col-span-1 mt-8 lg:mt-0">
                <div class="sticky top-24 space-y-6">
                    <!-- Exam Configuration Panel -->
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-success-50 p-2 rounded-lg">
                                <i class="fas fa-cog text-success text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-text-primary">Configuration</h3>
                                <p class="text-sm text-text-secondary">Exam settings</p>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <!-- Date Picker -->
                            <div>
                                <label for="examDate" class="block text-sm font-medium text-text-primary mb-2">
                                    Exam Date <span class="text-error">*</span>
                                </label>
                                <input type="date" id="examDate" name="examDate" class="form-input" value="2025-07-25" />
                            </div>

                            <!-- Time Picker -->
                            <div>
                                <label for="examTime" class="block text-sm font-medium text-text-primary mb-2">
                                    Start Time <span class="text-error">*</span>
                                </label>
                                <input type="time" id="examTime" name="examTime" class="form-input" value="10:00" />
                            </div>

                            <!-- Duration -->
                            <div>
                                <label for="examDuration" class="block text-sm font-medium text-text-primary mb-2">
                                    Duration (minutes) <span class="text-error">*</span>
                                </label>
                                <input type="number" id="examDuration" name="examDuration" class="form-input" placeholder="e.g., 90" value="90" min="1" max="300" />
                            </div>

                            <!-- Additional Settings -->
                            <div class="space-y-3">
                                <h4 class="text-sm font-medium text-text-primary">Additional Settings</h4>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="text-primary" checked />
                                    <span class="text-sm text-text-primary">Randomize question order</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="text-primary" />
                                    <span class="text-sm text-text-primary">Allow review before submit</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="text-primary" checked />
                                    <span class="text-sm text-text-primary">Auto-submit on time expiry</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Exam Summary -->
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <h3 class="text-lg font-medium text-text-primary mb-4">Exam Summary</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Questions:</span>
                                <span class="text-text-primary font-medium">2</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Students:</span>
                                <span class="text-text-primary font-medium">5</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Duration:</span>
                                <span class="text-text-primary font-medium">90 min</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Total Points:</span>
                                <span class="text-text-primary font-medium">2</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button id="createExamBtn" class="btn-primary w-full py-3 text-lg">
                            <i class="fas fa-rocket mr-2"></i>Create Exam
                        </button>
                        <button class="btn-secondary w-full">
                            <i class="fas fa-save mr-2"></i>Save as Draft
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-md w-full mx-4 p-6">
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-success-50 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-check-circle text-success text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-text-primary mb-2">Exam Created Successfully!</h3>
                <p class="text-text-secondary mb-6">Your exam has been created and is ready for students.</p>
                <div class="bg-secondary-50 rounded-lg p-4 mb-6">
                    <p class="text-sm text-text-secondary mb-2">Exam Link:</p>
                    <div class="flex items-center space-x-2">
                        <input type="text" value="https://examplatform.edu/exam/db-midterm-2025" class="form-input text-sm flex-1" readonly />
                        <button class="btn-primary px-3 py-2">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="btn-secondary flex-1" onclick="closeModal()">Close</button>
                    <a href="teacher_results_dashboard.html" class="btn-primary flex-1 text-center">View Results</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-surface border-t border-light mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <p class="text-text-secondary text-sm">
                    © 2025 ExamPro - Online Exam Platform. Developed by Dr. Ahmed Al-Rashid
                </p>
                <p class="text-text-secondary text-xs mt-2">
                    Contact: <EMAIL> | Department of Computer Science
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language toggle
        document.getElementById('languageToggle').addEventListener('click', function() {
            const currentLang = document.getElementById('currentLang');
            const isEnglish = currentLang.textContent === 'EN';
            currentLang.textContent = isEnglish ? 'AR' : 'EN';
            document.documentElement.dir = isEnglish ? 'rtl' : 'ltr';
            document.documentElement.lang = isEnglish ? 'ar' : 'en';
        });

        // CSV upload functionality
        const csvUploadArea = document.getElementById('csvUploadArea');
        const csvFileInput = document.getElementById('csvFileInput');
        const csvFeedback = document.getElementById('csvFeedback');

        csvUploadArea.addEventListener('click', () => csvFileInput.click());
        csvUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            csvUploadArea.classList.add('border-accent');
        });
        csvUploadArea.addEventListener('dragleave', () => {
            csvUploadArea.classList.remove('border-accent');
        });
        csvUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            csvUploadArea.classList.remove('border-accent');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleCSVFile(files[0]);
            }
        });

        csvFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleCSVFile(e.target.files[0]);
            }
        });

        function handleCSVFile(file) {
            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                showFeedback('Please select a valid CSV file.', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n');
                const headers = lines[0].split(',');
                
                if (!headers.includes('StudentID') || !headers.includes('StudentName')) {
                    showFeedback('CSV must contain StudentID and StudentName columns.', 'error');
                    return;
                }

                showFeedback(`Successfully imported ${lines.length - 1} students.`, 'success');
            };
            reader.readAsText(file);
        }

        function showFeedback(message, type) {
            csvFeedback.className = `mt-3 p-3 rounded-lg text-sm ${type === 'success' ? 'status-success' : 'status-error'}`;
            csvFeedback.textContent = message;
            csvFeedback.classList.remove('hidden');
            setTimeout(() => csvFeedback.classList.add('hidden'), 5000);
        }

        // Add student functionality
        document.getElementById('addStudentBtn').addEventListener('click', function() {
            const studentId = document.getElementById('studentId').value.trim();
            const studentName = document.getElementById('studentName').value.trim();
            
            if (!studentId || !studentName) {
                alert('Please fill in both Student ID and Student Name.');
                return;
            }
            
            // Clear form
            document.getElementById('studentId').value = '';
            document.getElementById('studentName').value = '';
            
            // Show success feedback
            showFeedback(`Student ${studentName} (${studentId}) added successfully.`, 'success');
        });

        // Add question functionality
        document.getElementById('addQuestionBtn').addEventListener('click', function() {
            const questionsList = document.getElementById('questionsList');
            const questionCount = questionsList.children.length + 1;
            
            const questionHTML = `
                <div class="question-card border border-light rounded-lg p-6 bg-secondary-50">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-md font-medium text-text-primary">Question ${questionCount}</h4>
                        <div class="flex items-center space-x-2">
                            <button class="text-text-secondary hover:text-primary transition-colors">
                                <i class="fas fa-expand-alt"></i>
                            </button>
                            <button class="text-error hover:text-error-600 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-text-primary mb-2">Question Text</label>
                            <textarea class="form-input h-24 resize-none" placeholder="Enter your question here..."></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3">
                                    <input type="radio" name="correct_answer_${questionCount}" value="A" class="text-primary">
                                    <label class="text-sm font-medium text-text-primary">A.</label>
                                    <input type="text" class="form-input flex-1" placeholder="Option A">
                                </div>
                                <div class="flex items-center space-x-3">
                                    <input type="radio" name="correct_answer_${questionCount}" value="B" class="text-primary">
                                    <label class="text-sm font-medium text-text-primary">B.</label>
                                    <input type="text" class="form-input flex-1" placeholder="Option B">
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3">
                                    <input type="radio" name="correct_answer_${questionCount}" value="C" class="text-primary">
                                    <label class="text-sm font-medium text-text-primary">C.</label>
                                    <input type="text" class="form-input flex-1" placeholder="Option C">
                                </div>
                                <div class="flex items-center space-x-3">
                                    <input type="radio" name="correct_answer_${questionCount}" value="D" class="text-primary">
                                    <label class="text-sm font-medium text-text-primary">D.</label>
                                    <input type="text" class="form-input flex-1" placeholder="Option D">
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4 pt-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="text-primary">
                                <span class="text-sm text-text-primary">Shuffle answer choices</span>
                            </label>
                        </div>
                    </div>
                </div>
            `;
            
            questionsList.insertAdjacentHTML('beforeend', questionHTML);
        });

        // Create exam functionality
        document.getElementById('createExamBtn').addEventListener('click', function() {
            const modal = document.getElementById('successModal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        });

        function closeModal() {
            const modal = document.getElementById('successModal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // Close modal when clicking outside
        document.getElementById('successModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>