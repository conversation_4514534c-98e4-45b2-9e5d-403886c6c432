<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Student Exam Login - Online Exam Platform</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fonlineexa5436back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface shadow-subtle border-b border-light sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L3 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-13-5z"/>
                            <path d="M14 14h4v2h-4v-2zm0-4h4v2h-4v-2zm0 8h4v2h-4v-2z" fill="white"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-text-primary">ExamPro</h1>
                        <p class="text-sm text-text-secondary">Online Exam Platform</p>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="teacher_exam_setup_dashboard.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-plus-circle mr-2"></i>Create Exam
                    </a>
                    <a href="teacher_results_dashboard.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>Results
                    </a>
                    <a href="student_exam_login.html" class="bg-primary-50 text-primary px-3 py-2 rounded-md text-sm font-medium border border-primary-200">
                        <i class="fas fa-user-graduate mr-2"></i>Student Portal
                    </a>
                </nav>

                <!-- Language Toggle -->
                <div class="flex items-center space-x-4">
                    <button id="languageToggle" class="text-text-secondary hover:text-primary transition-colors">
                        <i class="fas fa-globe mr-1"></i>
                        <span id="currentLang">EN</span>
                    </button>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn" class="text-text-secondary hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="md:hidden hidden bg-surface border-t border-light">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="teacher_exam_setup_dashboard.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-plus-circle mr-2"></i>Create Exam
                </a>
                <a href="teacher_results_dashboard.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-chart-bar mr-2"></i>Results
                </a>
                <a href="student_exam_login.html" class="bg-primary-50 text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-user-graduate mr-2"></i>Student Portal
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div class="max-w-md w-full space-y-8">
            <!-- Login Header -->
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-user-graduate text-primary text-2xl"></i>
                </div>
                <h2 class="text-3xl font-semibold text-text-primary mb-2">Student Exam Access</h2>
                <p class="text-text-secondary">Enter your credentials to access your examination</p>
            </div>

            <!-- Login Form -->
            <div class="bg-surface rounded-lg shadow-subtle border border-light p-8">
                <form id="loginForm" class="space-y-6">
                    <!-- Student ID Field -->
                    <div>
                        <label for="studentId" class="block text-sm font-medium text-text-primary mb-2">
                            Student ID <span class="text-error">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-id-card text-text-secondary"></i>
                            </div>
                            <input type="text" id="studentId" name="studentId" class="form-input pl-10" placeholder="e.g., ST001" required />
                        </div>
                    </div>

                    <!-- Exam Access Code Field -->
                    <div>
                        <label for="examCode" class="block text-sm font-medium text-text-primary mb-2">
                            Exam Access Code <span class="text-error">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-key text-text-secondary"></i>
                            </div>
                            <input type="password" id="examCode" name="examCode" class="form-input pl-10" placeholder="Enter exam access code" required />
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="errorMessage" class="hidden status-error rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span id="errorText">Invalid credentials. Please check your Student ID and Access Code.</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" id="loginBtn" class="btn-primary w-full py-3 text-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        <span id="loginBtnText">Enter Exam</span>
                    </button>
                </form>

                <!-- Additional Help -->
                <div class="mt-6 pt-6 border-t border-light">
                    <div class="text-center">
                        <p class="text-sm text-text-secondary mb-2">Need help accessing your exam?</p>
                        <button class="text-accent hover:text-accent-600 text-sm font-medium transition-colors">
                            <i class="fas fa-question-circle mr-1"></i>Contact Support
                        </button>
                    </div>
                </div>
            </div>

            <!-- Exam Information Card -->
            <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-accent-50 p-2 rounded-lg">
                        <i class="fas fa-info-circle text-accent text-lg"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-text-primary">Current Exam Information</h3>
                        <p class="text-sm text-text-secondary">Database Systems - Midterm Exam</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- Exam Details -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="bg-secondary-50 rounded-lg p-4">
                            <div class="flex items-center space-x-2 mb-2">
                                <i class="fas fa-clock text-warning"></i>
                                <span class="text-sm font-medium text-text-primary">Duration</span>
                            </div>
                            <p class="text-lg font-semibold text-text-primary">90 minutes</p>
                        </div>
                        <div class="bg-secondary-50 rounded-lg p-4">
                            <div class="flex items-center space-x-2 mb-2">
                                <i class="fas fa-question-circle text-primary"></i>
                                <span class="text-sm font-medium text-text-primary">Questions</span>
                            </div>
                            <p class="text-lg font-semibold text-text-primary">25 Questions</p>
                        </div>
                    </div>

                    <!-- Exam Schedule -->
                    <div class="bg-secondary-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-calendar-alt text-success"></i>
                                <span class="text-sm font-medium text-text-primary">Scheduled Time</span>
                            </div>
                            <span class="text-sm text-success-600 bg-success-50 px-2 py-1 rounded">
                                <i class="fas fa-circle mr-1"></i>Available Now
                            </span>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-text-secondary">Date:</span>
                                <span class="text-text-primary font-medium">July 25, 2025</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-text-secondary">Start Time:</span>
                                <span class="text-text-primary font-medium">10:00 AM</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-text-secondary">End Time:</span>
                                <span class="text-text-primary font-medium">11:30 AM</span>
                            </div>
                        </div>
                    </div>

                    <!-- Important Instructions -->
                    <div class="bg-warning-50 border border-warning-200 rounded-lg p-4">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-exclamation-triangle text-warning mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-warning-600 mb-2">Important Instructions</h4>
                                <ul class="text-sm text-warning-600 space-y-1">
                                    <li>• Ensure stable internet connection</li>
                                    <li>• Do not refresh or close the browser</li>
                                    <li>• Exam will auto-submit when time expires</li>
                                    <li>• Contact support immediately if technical issues occur</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Requirements -->
            <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                <h3 class="text-lg font-medium text-text-primary mb-4">System Requirements</h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-success"></i>
                            <span class="text-text-primary">Modern web browser</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-success"></i>
                            <span class="text-text-primary">Stable internet connection</span>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-success"></i>
                            <span class="text-text-primary">JavaScript enabled</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-success"></i>
                            <span class="text-text-primary">Screen resolution: 1024x768+</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-sm w-full mx-4 p-8">
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mb-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
                <h3 class="text-lg font-medium text-text-primary mb-2">Accessing Exam</h3>
                <p class="text-text-secondary">Please wait while we prepare your examination...</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-surface border-t border-light mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <p class="text-text-secondary text-sm">
                    © 2025 ExamPro - Online Exam Platform. Developed by Dr. Ahmed Al-Rashid
                </p>
                <p class="text-text-secondary text-xs mt-2">
                    Contact: <EMAIL> | Department of Computer Science
                </p>
                <div class="mt-4 flex justify-center space-x-6">
                    <button class="text-text-secondary hover:text-primary text-sm transition-colors">
                        <i class="fas fa-phone mr-1"></i>Technical Support
                    </button>
                    <button class="text-text-secondary hover:text-primary text-sm transition-colors">
                        <i class="fas fa-envelope mr-1"></i>Contact Admin
                    </button>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language toggle
        document.getElementById('languageToggle').addEventListener('click', function() {
            const currentLang = document.getElementById('currentLang');
            const isEnglish = currentLang.textContent === 'EN';
            currentLang.textContent = isEnglish ? 'AR' : 'EN';
            document.documentElement.dir = isEnglish ? 'rtl' : 'ltr';
            document.documentElement.lang = isEnglish ? 'ar' : 'en';
            
            // Update text content for RTL
            if (!isEnglish) {
                document.querySelector('h2').textContent = 'دخول الطلاب للامتحان';
                document.querySelector('p').textContent = 'أدخل بياناتك للوصول إلى الامتحان';
            } else {
                document.querySelector('h2').textContent = 'Student Exam Access';
                document.querySelector('p').textContent = 'Enter your credentials to access your examination';
            }
        });

        // Form validation and submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const studentId = document.getElementById('studentId').value.trim();
            const examCode = document.getElementById('examCode').value.trim();
            const errorMessage = document.getElementById('errorMessage');
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loadingModal = document.getElementById('loadingModal');
            
            // Clear previous errors
            errorMessage.classList.add('hidden');
            
            // Basic validation
            if (!studentId || !examCode) {
                showError('Please fill in all required fields.');
                return;
            }
            
            // Validate student ID format
            if (!/^ST\d{3}$/.test(studentId)) {
                showError('Student ID must be in format ST001, ST002, etc.');
                return;
            }
            
            // Mock authentication - check against predefined credentials
            const validCredentials = [
                { id: 'ST001', code: 'EXAM2025' },
                { id: 'ST002', code: 'EXAM2025' },
                { id: 'ST003', code: 'EXAM2025' },
                { id: 'ST004', code: 'EXAM2025' },
                { id: 'ST005', code: 'EXAM2025' }
            ];
            
            const isValid = validCredentials.some(cred => 
                cred.id === studentId && cred.code === examCode
            );
            
            if (!isValid) {
                showError('Invalid Student ID or Access Code. Please check your credentials.');
                return;
            }
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtnText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Verifying...';
            
            // Show loading modal
            loadingModal.classList.remove('hidden');
            loadingModal.classList.add('flex');
            
            // Simulate authentication delay
            setTimeout(() => {
                // Store student info in session storage
                sessionStorage.setItem('studentId', studentId);
                sessionStorage.setItem('examCode', examCode);
                sessionStorage.setItem('loginTime', new Date().toISOString());
                
                // Redirect to exam interface
                window.location.href = 'student_exam_interface.html';
            }, 2000);
        });
        
        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            
            // Auto-hide error after 5 seconds
            setTimeout(() => {
                errorMessage.classList.add('hidden');
            }, 5000);
        }
        
        // Input field enhancements
        document.getElementById('studentId').addEventListener('input', function(e) {
            // Auto-format student ID
            let value = e.target.value.toUpperCase();
            if (value.length > 0 && !value.startsWith('ST')) {
                value = 'ST' + value.replace(/[^0-9]/g, '');
            }
            if (value.length > 5) {
                value = value.substring(0, 5);
            }
            e.target.value = value;
        });
        
        // Clear error message when user starts typing
        document.getElementById('studentId').addEventListener('input', clearError);
        document.getElementById('examCode').addEventListener('input', clearError);
        
        function clearError() {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.classList.add('hidden');
        }
        
        // Check if user is already logged in
        window.addEventListener('load', function() {
            const studentId = sessionStorage.getItem('studentId');
            const examCode = sessionStorage.getItem('examCode');
            
            if (studentId && examCode) {
                // User is already logged in, redirect to exam
                window.location.href = 'student_exam_interface.html';
            }
        });
        
        // Prevent form submission on Enter key in password field
        document.getElementById('examCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
        
        // Session timeout warning (30 minutes)
        let sessionTimeout;
        
        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            sessionTimeout = setTimeout(() => {
                alert('Your session will expire in 5 minutes due to inactivity. Please log in again if needed.');
            }, 25 * 60 * 1000); // 25 minutes
        }
        
        // Reset timeout on user activity
        document.addEventListener('click', resetSessionTimeout);
        document.addEventListener('keypress', resetSessionTimeout);
        
        // Initialize session timeout
        resetSessionTimeout();
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>