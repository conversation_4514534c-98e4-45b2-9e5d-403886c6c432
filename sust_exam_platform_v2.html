<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة الامتحانات الإلكترونية - جامعة السودان للعلوم والتكنولوجيا</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #1e3a8a;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        body.en {
            font-family: 'Inter', 'Cairo', sans-serif;
            direction: ltr;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: var(--white);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .language-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .language-toggle:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* Author Info */
        .author-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .author-info .author-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .author-info .contact {
            opacity: 0.9;
            margin: 2px 0;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            background: var(--white);
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--text-light);
        }

        .nav-tab.active {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .nav-tab:hover:not(.active) {
            background: var(--light-bg);
            color: var(--primary-color);
        }

        /* Main Content */
        .view {
            display: none;
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            box-shadow: var(--shadow-lg);
            animation: fadeIn 0.5s ease;
        }

        .view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Form Styles */
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            background: var(--light-bg);
        }

        .form-section h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--text-dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Button Styles */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background: var(--text-light);
            color: white;
        }

        /* Question Types */
        .question-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .question-type-card {
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: var(--white);
        }

        .question-type-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .question-type-card.selected {
            border-color: var(--primary-color);
            background: rgba(44, 90, 160, 0.1);
        }

        .question-type-card i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .question-type-selector {
                grid-template-columns: 1fr;
            }
        }

        /* Additional Styles */
        .exam-link-container {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }

        .exam-link-container input {
            flex: 1;
            max-width: 400px;
        }

        .btn-lg {
            padding: 15px 30px;
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        /* Student Exam Interface Styles */
        .exam-header {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .exam-info h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .exam-info p {
            color: var(--text-light);
            margin: 0;
        }

        .timer-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .timer {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .timer.warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            animation: pulse 1s infinite;
        }

        .timer.danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            animation: pulse 0.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .question-container {
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-lg);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .question-number {
            background: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
        }

        .question-navigation {
            display: flex;
            gap: 10px;
        }

        .question-content {
            margin-bottom: 25px;
        }

        .question-text {
            font-size: 1.2rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .question-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .option-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .option-item:hover {
            border-color: var(--accent-color);
            background: rgba(59, 130, 246, 0.05);
        }

        .option-item.selected {
            border-color: var(--primary-color);
            background: rgba(44, 90, 160, 0.1);
        }

        .option-item input[type="radio"] {
            margin-right: 12px;
            margin-left: 12px;
            transform: scale(1.2);
        }

        .option-label {
            font-weight: 500;
            margin-right: 10px;
            margin-left: 10px;
            color: var(--primary-color);
        }

        .option-text {
            flex: 1;
            font-size: 1rem;
            color: var(--text-dark);
        }

        .exam-footer {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .progress-bar {
            background: var(--border-color);
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .exam-disabled {
            pointer-events: none;
            opacity: 0.6;
            filter: grayscale(50%);
        }

        .submission-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .submission-modal {
            background: var(--white);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
            box-shadow: var(--shadow-lg);
        }

        .submission-modal h3 {
            color: var(--success-color);
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .submission-modal p {
            color: var(--text-light);
            margin-bottom: 20px;
        }

        .results-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-info p {
            color: var(--text-light);
            margin: 0;
        }

        .results-table-container {
            background: var(--white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .results-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .results-table tbody tr:hover {
            background: var(--light-bg);
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-left { text-align: left; }
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }
        .mt-20 { margin-top: 20px; }
        .hidden { display: none; }
        .flex { display: flex; }
        .flex-center { display: flex; align-items: center; justify-content: center; }
        .gap-10 { gap: 10px; }
        .gap-15 { gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="language-toggle" onclick="toggleLanguage()">
                <i class="fas fa-globe"></i>
                <span id="langText">English</span>
            </button>
            
            <h1 id="mainTitle">منصة الامتحانات الإلكترونية</h1>
            <p class="subtitle" id="subtitle">جامعة السودان للعلوم والتكنولوجيا</p>
        </div>

        <!-- Author Information -->
        <div class="author-info">
            <div class="author-name" id="authorName">د. محمد يعقوب إسماعيل</div>
            <div class="contact" id="authorTitle">جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية</div>
            <div class="contact">© 2025 | <EMAIL></div>
            <div class="contact">+249912867327 | +966538076790</div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showView('teacher')" id="teacherTab">
                <i class="fas fa-chalkboard-teacher"></i>
                <span id="teacherTabText">إعداد الامتحان</span>
            </button>
            <button class="nav-tab" onclick="showView('student')" id="studentTab">
                <i class="fas fa-user-graduate"></i>
                <span id="studentTabText">امتحان الطالب</span>
            </button>
            <button class="nav-tab" onclick="showView('results')" id="resultsTab">
                <i class="fas fa-chart-bar"></i>
                <span id="resultsTabText">النتائج</span>
            </button>
        </div>

        <!-- Teacher's Setup View -->
        <div id="teacherView" class="view active">
            <!-- Academic Information Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-university"></i>
                    <span id="academicInfoTitle">المعلومات الأكاديمية</span>
                </h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="college" id="collegeLabel">الكلية</label>
                        <select id="college" onchange="updateMajors()">
                            <option value="" id="selectCollegeOption">اختر الكلية</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="major" id="majorLabel">التخصص</label>
                        <select id="major" disabled>
                            <option value="" id="selectMajorOption">اختر التخصص أولاً</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="academicYear" id="academicYearLabel">السنة الدراسية</label>
                        <select id="academicYear" onchange="updateSpecializations()">
                            <option value="" id="selectYearOption">اختر السنة الدراسية</option>
                            <option value="1">السنة الأولى</option>
                            <option value="2">السنة الثانية</option>
                            <option value="3">السنة الثالثة</option>
                            <option value="4">السنة الرابعة</option>
                            <option value="5">السنة الخامسة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="semester" id="semesterLabel">الفصل الدراسي</label>
                        <select id="semester" onchange="updateSpecializations()">
                            <option value="" id="selectSemesterOption">اختر الفصل الدراسي</option>
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="courseName" id="courseNameLabel">اسم المقرر</label>
                        <input type="text" id="courseName" placeholder="أدخل اسم المقرر">
                    </div>

                    <div class="form-group">
                        <label for="examTitle" id="examTitleLabel">عنوان الامتحان</label>
                        <input type="text" id="examTitle" placeholder="أدخل عنوان الامتحان">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="examDate" id="examDateLabel">تاريخ الامتحان</label>
                        <input type="datetime-local" id="examDate">
                    </div>

                    <div class="form-group">
                        <label for="examDuration" id="examDurationLabel">مدة الامتحان (بالدقائق)</label>
                        <input type="number" id="examDuration" min="1" max="300" placeholder="60">
                    </div>
                </div>
            </div>

            <!-- Student Management Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-users"></i>
                    <span id="studentMgmtTitle">إدارة الطلاب</span>
                </h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="studentId" id="studentIdLabel">رقم الطالب</label>
                        <input type="text" id="studentId" placeholder="أدخل رقم الطالب">
                    </div>

                    <div class="form-group">
                        <label for="studentName" id="studentNameLabel">اسم الطالب</label>
                        <input type="text" id="studentName" placeholder="أدخل اسم الطالب">
                    </div>

                    <div class="form-group flex-center">
                        <button class="btn btn-primary" onclick="addStudent()" id="addStudentBtn">
                            <i class="fas fa-plus"></i>
                            <span>إضافة طالب</span>
                        </button>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="csvFile" id="csvFileLabel">رفع ملف CSV للطلاب</label>
                        <input type="file" id="csvFile" accept=".csv" onchange="handleCSVUpload(event)">
                    </div>
                </div>

                <div id="studentsList" class="mt-20">
                    <h4 id="studentsListTitle">قائمة الطلاب المسجلين:</h4>
                    <div id="studentsContainer"></div>
                </div>
            </div>

            <!-- Question Types Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-question-circle"></i>
                    <span id="questionTypesTitle">أنواع الأسئلة</span>
                </h3>

                <div class="question-type-selector">
                    <div class="question-type-card" onclick="selectQuestionType('mcq')" data-type="mcq">
                        <i class="fas fa-list-ul"></i>
                        <h4 id="mcqTitle">أسئلة الاختيار من متعدد</h4>
                        <p id="mcqDesc">MCQ Questions</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('short')" data-type="short">
                        <i class="fas fa-edit"></i>
                        <h4 id="shortTitle">أسئلة قصيرة</h4>
                        <p id="shortDesc">Short Answer Questions</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('fillblank')" data-type="fillblank">
                        <i class="fas fa-fill-drip"></i>
                        <h4 id="fillBlankTitle">ملء الفراغات</h4>
                        <p id="fillBlankDesc">Fill in the Blanks</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('diagram')" data-type="diagram">
                        <i class="fas fa-project-diagram"></i>
                        <h4 id="diagramTitle">تسمية الأجزاء</h4>
                        <p id="diagramDesc">Name Parts in Figures</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('flowchart')" data-type="flowchart">
                        <i class="fas fa-sitemap"></i>
                        <h4 id="flowchartTitle">المخططات الانسيابية</h4>
                        <p id="flowchartDesc">Flowchart Questions</p>
                    </div>
                </div>
            </div>

            <!-- Question Creation Section -->
            <div class="form-section" id="questionCreationSection" style="display: none;">
                <h3>
                    <i class="fas fa-plus-circle"></i>
                    <span id="createQuestionTitle">إنشاء الأسئلة</span>
                </h3>

                <div id="questionForm">
                    <!-- Dynamic question form will be inserted here -->
                </div>

                <div class="text-center mt-20">
                    <button type="button" class="btn btn-success" onclick="addQuestion()" id="addQuestionBtn">
                        <i class="fas fa-plus"></i>
                        <span>إضافة السؤال</span>
                    </button>
                </div>
            </div>

            <!-- Questions List -->
            <div class="form-section" id="questionsListSection" style="display: none;">
                <h3>
                    <i class="fas fa-list"></i>
                    <span id="questionsListTitle">قائمة الأسئلة</span>
                </h3>

                <div id="questionsList"></div>
            </div>

            <!-- Generate Exam Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-link"></i>
                    <span id="generateExamTitle">إنشاء رابط الامتحان</span>
                </h3>

                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg" onclick="generateExam()" id="generateExamBtn">
                        <i class="fas fa-rocket"></i>
                        <span>إنشاء الامتحان</span>
                    </button>
                </div>

                <div id="examLinkSection" class="mt-20 text-center" style="display: none;">
                    <h4 id="examLinkTitle">رابط الامتحان:</h4>
                    <div class="exam-link-container">
                        <input type="text" id="examLink" readonly class="form-control">
                        <button type="button" class="btn btn-secondary" onclick="copyExamLink()" id="copyLinkBtn">
                            <i class="fas fa-copy"></i>
                            <span>نسخ الرابط</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student's Exam View -->
        <div id="studentView" class="view">
            <!-- Student Login Section -->
            <div id="studentLoginSection" class="text-center mb-20">
                <h2 id="studentWelcomeTitle">مرحباً بك في الامتحان</h2>
                <p id="studentWelcomeDesc">يرجى إدخال رقم الطالب للبدء</p>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="studentLoginId" id="studentLoginLabel">رقم الطالب</label>
                            <input type="text" id="studentLoginId" placeholder="أدخل رقم الطالب">
                        </div>

                        <div class="form-group flex-center">
                            <button type="button" class="btn btn-primary" onclick="startExam()" id="startExamBtn">
                                <i class="fas fa-play"></i>
                                <span>بدء الامتحان</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Exam Interface (Hidden initially) -->
            <div id="examInterface" class="hidden">
                <!-- Exam Header with Timer -->
                <div class="exam-header">
                    <div class="exam-info">
                        <h3 id="examInfoTitle">امتحان الرياضيات</h3>
                        <p id="examInfoDetails">كلية الهندسة - مدرسة الهندسة المدنية</p>
                    </div>

                    <div class="timer-container">
                        <div class="timer" id="timer">
                            <i class="fas fa-clock"></i>
                            <span id="timeRemaining">60:00</span>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>

                <!-- Question Container -->
                <div class="question-container" id="questionContainer">
                    <div class="question-header">
                        <span class="question-number" id="questionNumber">السؤال 1 من 10</span>
                        <div class="question-navigation">
                            <button type="button" class="btn btn-secondary" onclick="previousQuestion()" id="prevBtn" disabled>
                                <i class="fas fa-arrow-right"></i>
                                <span id="prevBtnText">السابق</span>
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="nextQuestion()" id="nextBtn">
                                <span id="nextBtnText">التالي</span>
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>

                    <div class="question-content">
                        <div class="question-text" id="questionText">
                            ما هو ناتج 2 + 2؟
                        </div>

                        <div class="question-options" id="questionOptions">
                            <div class="option-item" onclick="selectOption(0)">
                                <input type="radio" name="answer" value="0" id="option0">
                                <span class="option-label">أ.</span>
                                <span class="option-text">3</span>
                            </div>
                            <div class="option-item" onclick="selectOption(1)">
                                <input type="radio" name="answer" value="1" id="option1">
                                <span class="option-label">ب.</span>
                                <span class="option-text">4</span>
                            </div>
                            <div class="option-item" onclick="selectOption(2)">
                                <input type="radio" name="answer" value="2" id="option2">
                                <span class="option-label">ج.</span>
                                <span class="option-text">5</span>
                            </div>
                            <div class="option-item" onclick="selectOption(3)">
                                <input type="radio" name="answer" value="3" id="option3">
                                <span class="option-label">د.</span>
                                <span class="option-text">6</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exam Footer with Submit Button -->
                <div class="exam-footer">
                    <button type="button" class="btn btn-success btn-lg" onclick="submitExam()" id="submitExamBtn">
                        <i class="fas fa-check"></i>
                        <span id="submitBtnText">تسليم الامتحان</span>
                    </button>
                </div>
            </div>

            <!-- Submission Overlay (Hidden initially) -->
            <div id="submissionOverlay" class="submission-overlay hidden">
                <div class="submission-modal">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--success-color); margin-bottom: 15px;"></i>
                    <h3 id="submissionTitle">تم تسليم الامتحان بنجاح</h3>
                    <p id="submissionMessage">شكراً لك، تم حفظ إجاباتك وسيتم إعلان النتائج قريباً</p>
                    <button type="button" class="btn btn-primary" onclick="closeSubmissionModal()" id="closeModalBtn">
                        <span>إغلاق</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Teacher's Results View -->
        <div id="resultsView" class="view">
            <div class="text-center mb-20">
                <h2 id="resultsTitle">نتائج الامتحان</h2>
                <p id="resultsDesc">عرض وتحليل نتائج الطلاب</p>
            </div>

            <div id="resultsContent">
                <p id="noResultsMsg">لا توجد نتائج متاحة حالياً</p>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let currentLanguage = 'ar';
        let sustData = {};
        let students = [];
        let questions = [];
        let currentQuestionType = '';
        let examData = {};

        // Language Data
        const translations = {
            ar: {
                mainTitle: "منصة الامتحانات الإلكترونية",
                subtitle: "جامعة السودان للعلوم والتكنولوجيا",
                authorName: "د. محمد يعقوب إسماعيل",
                authorTitle: "جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية",
                teacherTabText: "إعداد الامتحان",
                studentTabText: "امتحان الطالب",
                resultsTabText: "النتائج",
                academicInfoTitle: "المعلومات الأكاديمية",
                collegeLabel: "الكلية",
                majorLabel: "التخصص",
                academicYearLabel: "السنة الدراسية",
                semesterLabel: "الفصل الدراسي",
                courseNameLabel: "اسم المقرر",
                examTitleLabel: "عنوان الامتحان",
                examDateLabel: "تاريخ الامتحان",
                examDurationLabel: "مدة الامتحان (بالدقائق)",
                studentMgmtTitle: "إدارة الطلاب",
                studentIdLabel: "رقم الطالب",
                studentNameLabel: "اسم الطالب",
                csvFileLabel: "رفع ملف CSV للطلاب",
                studentsListTitle: "قائمة الطلاب المسجلين:",
                questionTypesTitle: "أنواع الأسئلة",
                mcqTitle: "أسئلة الاختيار من متعدد",
                shortTitle: "أسئلة قصيرة",
                fillBlankTitle: "ملء الفراغات",
                diagramTitle: "تسمية الأجزاء",
                flowchartTitle: "المخططات الانسيابية",
                createQuestionTitle: "إنشاء الأسئلة",
                questionsListTitle: "قائمة الأسئلة",
                generateExamTitle: "إنشاء رابط الامتحان",
                examLinkTitle: "رابط الامتحان:",
                studentWelcomeTitle: "مرحباً بك في الامتحان",
                studentWelcomeDesc: "يرجى إدخال رقم الطالب للبدء",
                studentLoginLabel: "رقم الطالب",
                resultsTitle: "نتائج الامتحان",
                resultsDesc: "عرض وتحليل نتائج الطلاب",
                noResultsMsg: "لا توجد نتائج متاحة حالياً"
            },
            en: {
                mainTitle: "Online Exam Platform",
                subtitle: "Sudan University of Science and Technology",
                authorName: "Dr. Mohammed Yagoub Esmail",
                authorTitle: "Sudan University of Science and Technology - BME Department",
                teacherTabText: "Exam Setup",
                studentTabText: "Student Exam",
                resultsTabText: "Results",
                academicInfoTitle: "Academic Information",
                collegeLabel: "College",
                majorLabel: "Major/Specialization",
                academicYearLabel: "Academic Year",
                semesterLabel: "Semester",
                courseNameLabel: "Course Name",
                examTitleLabel: "Exam Title",
                examDateLabel: "Exam Date",
                examDurationLabel: "Exam Duration (minutes)",
                studentMgmtTitle: "Student Management",
                studentIdLabel: "Student ID",
                studentNameLabel: "Student Name",
                csvFileLabel: "Upload CSV File for Students",
                studentsListTitle: "Registered Students:",
                questionTypesTitle: "Question Types",
                mcqTitle: "Multiple Choice Questions",
                shortTitle: "Short Answer Questions",
                fillBlankTitle: "Fill in the Blanks",
                diagramTitle: "Name Parts in Figures",
                flowchartTitle: "Flowchart Questions",
                createQuestionTitle: "Create Questions",
                questionsListTitle: "Questions List",
                generateExamTitle: "Generate Exam Link",
                examLinkTitle: "Exam Link:",
                studentWelcomeTitle: "Welcome to the Exam",
                studentWelcomeDesc: "Please enter your student ID to begin",
                studentLoginLabel: "Student ID",
                resultsTitle: "Exam Results",
                resultsDesc: "View and analyze student results",
                noResultsMsg: "No results available currently"
            }
        };

        // Initialize SUST Data
        function initializeSustData() {
            sustData = {
                "كلية الهندسة": [
                    "مدرسة الهندسة المدنية - هندسة الإنشاءات",
                    "مدرسة الهندسة المدنية - هندسة الموارد المائية والبيئية",
                    "مدرسة الهندسة المدنية - هندسة النقل والطرق",
                    "مدرسة الهندسة المدنية - إدارة التشييد",
                    "مدرسة الهندسة الميكانيكية - هندسة القوى والحراريات",
                    "مدرسة الهندسة الميكانيكية - التصميم والإنتاج",
                    "مدرسة الهندسة الميكانيكية - الهندسة الصناعية",
                    "مدرسة الهندسة الميكانيكية - الميكاترونكس",
                    "مدرسة هندسة الكهرباء - هندسة القدرة والآلات الكهربائية",
                    "مدرسة هندسة الكهرباء - هندسة التحكم",
                    "مدرسة هندسة المساحة - الجيوديسيا",
                    "مدرسة هندسة المساحة - التصوير المساحي والاستشعار عن بعد",
                    "مدرسة هندسة المساحة - نظم المعلومات الجغرافية",
                    "مدرسة هندسة الإلكترونيات - هندسة الاتصالات",
                    "مدرسة هندسة الإلكترونيات - الأنظمة المدمجة",
                    "مدرسة هندسة الإلكترونيات - هندسة الحاسوب والشبكات",
                    "قسم الهندسة الطبية الحيوية",
                    "قسم هندسة الطيران"
                ],
                "كلية هندسة العمارة والتخطيط العمراني": ["هندسة العمارة", "التخطيط العمراني"],
                "كلية الهندسة الصناعية الكيميائية": ["الهندسة الكيميائية", "هندسة العمليات الصناعية", "الصناعات البتروكيماوية", "الصناعات الدوائية", "الصناعات الغذائية"],
                "كلية هندسة النفط": ["هندسة استكشاف النفط", "هندسة استخراج النفط", "هندسة إنتاج النفط والغاز"],
                "كلية هندسة المياه (ود المقبول)": ["هندسة الموارد المائية", "هندسة معالجة المياه", "هندسة الري والصرف"],
                "كلية الطب البشري": ["الطب العام", "الجراحة العامة", "الطب الباطني", "طب الأطفال", "النساء والتوليد", "طب المجتمع"],
                "كلية الصيدلة": ["الصيدلة الإكلينيكية", "الكيمياء الصيدلانية", "علم الأدوية", "الصيدلانيات"],
                "كلية طب الأسنان": ["طب الأسنان العام", "جراحة الفم والأسنان", "تقويم الأسنان", "طب أسنان الأطفال"],
                "كلية علوم الحاسوب وتقنية المعلومات": ["علوم الحاسوب", "تقنية المعلومات", "نظم المعلومات"],
                "كلية العلوم": ["الفيزياء", "الكيمياء", "الرياضيات", "علوم الأرض (الجيولوجيا)"],
                "كلية الدراسات التجارية": ["المحاسبة", "إدارة الأعمال", "التسويق", "التمويل والمصارف"],
                "كلية اللغات": ["اللغة الإنجليزية", "اللغة الفرنسية", "اللغة العربية", "اللغة الألمانية"],
                "كلية الفنون الجميلة والتطبيقية": ["التصميم الصناعي", "التصميم الإيضاحي", "الطباعة والنشر", "الخزف", "النسيج", "الرسم والتصوير", "النحت"],
                "كلية التربية": ["التربية التقنية", "التربية الأسرية", "التربية الفنية", "علم النفس التربوي", "المناهج وطرق التدريس"],
                "كلية التربية البدنية": ["التربية البدنية والرياضة", "علوم الحركة", "الإدارة الرياضية", "التدريب الرياضي"],
                "كلية الموسيقى والدراما": ["الموسيقى", "الدراما", "الفنون المسرحية", "التأليف الموسيقي"],
                "كلية الإنتاج الحيواني": ["الإنتاج الحيواني", "علوم الألبان", "تربية الدواجن", "التغذية الحيوانية"],
                "كلية علوم الأشعة الطبية": ["التصوير الطبي", "الأشعة التشخيصية", "الطب النووي", "العلاج الإشعاعي"],
                "كلية المختبرات الطبية": ["علم الأمراض", "الكيمياء الحيوية الطبية", "علم الأحياء الدقيقة الطبية", "علم أمراض الدم"],
                "كلية تكنولوجيا النفط والغاز": ["تكنولوجيا تشغيل المصافي", "تكنولوجيا إنتاج النفط والغاز"],
                "كلية علوم الاتصال": ["العلاقات العامة والإعلان", "الصحافة والنشر الإلكتروني", "الإذاعة والتلفزيون"],
                "كلية الزراعة": ["الإنتاج النباتي", "علوم وتكنولوجيا الأغذية", "الهندسة الزراعية"],
                "كلية الطب البيطري": ["الطب البيطري"],
                "كلية الغابات والمراعي": ["علوم الغابات", "علوم المراعي"],
                "معهد الليزر": ["تطبيقات الليزر الطبية", "تطبيقات الليزر الصناعية", "فيزياء الليزر", "تقنيات الليزر المتقدمة"]
            };
        }

        // Initialize the application
        function initializeApp() {
            initializeSustData();
            populateColleges();
            updateLanguage();
        }

        // Language Toggle Function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage();
        }

        // Update Language
        function updateLanguage() {
            const body = document.body;
            const langText = document.getElementById('langText');

            if (currentLanguage === 'en') {
                body.classList.add('en');
                body.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', 'en');
                langText.textContent = 'العربية';
            } else {
                body.classList.remove('en');
                body.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
                langText.textContent = 'English';
            }

            // Update all text elements
            const texts = translations[currentLanguage];
            Object.keys(texts).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = texts[key];
                }
            });
        }

        // Populate Colleges
        function populateColleges() {
            const collegeSelect = document.getElementById('college');
            collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';

            Object.keys(sustData).forEach(college => {
                const option = document.createElement('option');
                option.value = college;
                option.textContent = college;
                collegeSelect.appendChild(option);
            });
        }

        // Update Majors based on College selection
        function updateMajors() {
            const collegeSelect = document.getElementById('college');
            const majorSelect = document.getElementById('major');
            const selectedCollege = collegeSelect.value;

            majorSelect.innerHTML = '<option value="">اختر التخصص</option>';
            majorSelect.disabled = !selectedCollege;

            if (selectedCollege && sustData[selectedCollege]) {
                sustData[selectedCollege].forEach(major => {
                    const option = document.createElement('option');
                    option.value = major;
                    option.textContent = major;
                    majorSelect.appendChild(option);
                });
                majorSelect.disabled = false;
            }
        }

        // Update Specializations based on Year and Semester
        function updateSpecializations() {
            const year = document.getElementById('academicYear').value;
            const semester = document.getElementById('semester').value;
            const majorSelect = document.getElementById('major');

            // Logic for engineering specializations starting from 4th year, 8th semester
            if (year && semester) {
                const semesterNumber = (parseInt(year) - 1) * 2 + parseInt(semester);

                // If semester 8 or higher, show detailed specializations
                if (semesterNumber >= 8) {
                    updateMajors(); // Keep current detailed specializations
                } else {
                    // Show general school names only
                    updateMajorsForEarlyYears();
                }
            }
        }

        // Update majors for early years (general school names only)
        function updateMajorsForEarlyYears() {
            const collegeSelect = document.getElementById('college');
            const majorSelect = document.getElementById('major');
            const selectedCollege = collegeSelect.value;

            if (selectedCollege === "كلية الهندسة") {
                majorSelect.innerHTML = '<option value="">اختر التخصص</option>';
                const generalMajors = [
                    "مدرسة الهندسة المدنية",
                    "مدرسة الهندسة الميكانيكية",
                    "مدرسة هندسة الكهرباء",
                    "مدرسة هندسة المساحة",
                    "مدرسة هندسة الإلكترونيات",
                    "قسم الهندسة الطبية الحيوية",
                    "قسم هندسة الطيران"
                ];

                generalMajors.forEach(major => {
                    const option = document.createElement('option');
                    option.value = major;
                    option.textContent = major;
                    majorSelect.appendChild(option);
                });
            } else {
                updateMajors(); // For non-engineering colleges, use normal majors
            }
        }

        // Show View Function
        function showView(viewName) {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected view
            document.getElementById(viewName + 'View').classList.add('active');

            // Add active class to selected tab
            document.getElementById(viewName + 'Tab').classList.add('active');
        }

        // Add Student Function
        function addStudent() {
            const studentId = document.getElementById('studentId').value.trim();
            const studentName = document.getElementById('studentName').value.trim();

            if (!studentId || !studentName) {
                alert('يرجى إدخال رقم الطالب والاسم');
                return;
            }

            // Check if student already exists
            if (students.find(s => s.id === studentId)) {
                alert('هذا الطالب مسجل مسبقاً');
                return;
            }

            students.push({ id: studentId, name: studentName });
            updateStudentsList();

            // Clear inputs
            document.getElementById('studentId').value = '';
            document.getElementById('studentName').value = '';
        }

        // Update Students List
        function updateStudentsList() {
            const container = document.getElementById('studentsContainer');
            container.innerHTML = '';

            students.forEach((student, index) => {
                const studentDiv = document.createElement('div');
                studentDiv.className = 'student-item';
                studentDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px;';
                studentDiv.innerHTML = `
                    <span>${student.id} - ${student.name}</span>
                    <button type="button" class="btn btn-danger" style="padding: 5px 10px; font-size: 0.8rem;" onclick="removeStudent(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(studentDiv);
            });
        }

        // Remove Student Function
        function removeStudent(index) {
            students.splice(index, 1);
            updateStudentsList();
        }

        // Handle CSV Upload
        function handleCSVUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n').filter(line => line.trim());
                let addedCount = 0;

                lines.forEach(line => {
                    const [id, name] = line.split(',').map(item => item.trim());
                    if (id && name && !students.find(s => s.id === id)) {
                        students.push({ id, name });
                        addedCount++;
                    }
                });

                updateStudentsList();
                alert(`تم إضافة ${addedCount} طالب من الملف`);
            };

            reader.readAsText(file);
        }

        // Student Exam Variables
        let currentExam = null;
        let currentQuestionIndex = 0;
        let studentAnswers = [];
        let examTimer = null;
        let timeRemaining = 0;
        let examStartTime = null;
        let isExamSubmitted = false;

        // Start Exam Function
        function startExam() {
            const studentId = document.getElementById('studentLoginId').value.trim();

            if (!studentId) {
                const message = currentLanguage === 'ar' ? 'يرجى إدخال رقم الطالب' : 'Please enter student ID';
                alert(message);
                return;
            }

            // Check if exam data exists (in real app, this would be from URL parameter)
            if (!examData || !examData.students || examData.students.length === 0) {
                const message = currentLanguage === 'ar' ? 'لا يوجد امتحان متاح حالياً' : 'No exam available currently';
                alert(message);
                return;
            }

            // Verify student is in the list
            const student = examData.students.find(s => s.id === studentId);
            if (!student) {
                const message = currentLanguage === 'ar' ? 'رقم الطالب غير مسجل في هذا الامتحان' : 'Student ID not registered for this exam';
                alert(message);
                return;
            }

            // Check if student already took the exam
            const existingResult = examData.results?.find(r => r.studentId === studentId);
            if (existingResult) {
                const message = currentLanguage === 'ar' ? 'لقد قمت بأداء هذا الامتحان مسبقاً' : 'You have already taken this exam';
                alert(message);
                return;
            }

            // Initialize exam
            currentExam = { ...examData };
            currentQuestionIndex = 0;
            studentAnswers = new Array(currentExam.questions.length).fill(null);
            timeRemaining = currentExam.duration * 60; // Convert minutes to seconds
            examStartTime = new Date();
            isExamSubmitted = false;

            // Shuffle questions and options if needed
            shuffleExamContent();

            // Hide login section and show exam interface
            document.getElementById('studentLoginSection').classList.add('hidden');
            document.getElementById('examInterface').classList.remove('hidden');

            // Update exam info
            updateExamInfo();

            // Start timer
            startExamTimer();

            // Display first question
            displayCurrentQuestion();

            // Prevent page refresh/close
            window.addEventListener('beforeunload', preventPageLeave);
        }

        // Shuffle Exam Content
        function shuffleExamContent() {
            // Shuffle questions order
            currentExam.questions = shuffleArray(currentExam.questions);

            // Shuffle options for MCQ questions
            currentExam.questions.forEach(question => {
                if (question.type === 'mcq') {
                    const correctAnswer = question.correctAnswer;
                    const correctText = question.options[correctAnswer - 1];

                    // Shuffle options
                    question.options = shuffleArray(question.options);

                    // Update correct answer index
                    question.correctAnswer = question.options.indexOf(correctText) + 1;
                }
            });
        }

        // Shuffle Array Function
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }

        // Update Exam Info
        function updateExamInfo() {
            document.getElementById('examInfoTitle').textContent = currentExam.examTitle;
            document.getElementById('examInfoDetails').textContent = `${currentExam.college} - ${currentExam.major}`;
        }

        // Start Exam Timer
        function startExamTimer() {
            examTimer = setInterval(() => {
                if (timeRemaining <= 0) {
                    // Time's up - auto submit
                    autoSubmitExam();
                    return;
                }

                timeRemaining--;
                updateTimerDisplay();

                // Change timer color based on remaining time
                updateTimerColor();

            }, 1000);
        }

        // Update Timer Display
        function updateTimerDisplay() {
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timeRemaining').textContent = timeString;
        }

        // Update Timer Color
        function updateTimerColor() {
            const timer = document.getElementById('timer');
            const totalTime = currentExam.duration * 60;
            const percentage = (timeRemaining / totalTime) * 100;

            timer.classList.remove('warning', 'danger');

            if (percentage <= 10) {
                timer.classList.add('danger');
            } else if (percentage <= 25) {
                timer.classList.add('warning');
            }
        }

        // Display Current Question
        function displayCurrentQuestion() {
            const question = currentExam.questions[currentQuestionIndex];
            const totalQuestions = currentExam.questions.length;

            // Update question number
            const questionNumberText = currentLanguage === 'ar' ?
                `السؤال ${currentQuestionIndex + 1} من ${totalQuestions}` :
                `Question ${currentQuestionIndex + 1} of ${totalQuestions}`;
            document.getElementById('questionNumber').textContent = questionNumberText;

            // Update progress bar
            const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;

            // Update question text
            document.getElementById('questionText').textContent = question.text;

            // Update options for MCQ questions
            if (question.type === 'mcq') {
                updateMCQOptions(question);
            }

            // Update navigation buttons
            updateNavigationButtons();
        }

        // Update MCQ Options
        function updateMCQOptions(question) {
            const optionsContainer = document.getElementById('questionOptions');
            optionsContainer.innerHTML = '';

            const optionLabels = ['أ', 'ب', 'ج', 'د'];

            question.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option-item';
                optionDiv.onclick = () => selectOption(index);

                const isSelected = studentAnswers[currentQuestionIndex] === index;
                if (isSelected) {
                    optionDiv.classList.add('selected');
                }

                optionDiv.innerHTML = `
                    <input type="radio" name="answer" value="${index}" id="option${index}" ${isSelected ? 'checked' : ''}>
                    <span class="option-label">${optionLabels[index]}.</span>
                    <span class="option-text">${option}</span>
                `;

                optionsContainer.appendChild(optionDiv);
            });
        }

        // Select Option
        function selectOption(optionIndex) {
            if (isExamSubmitted) return;

            // Update student answer
            studentAnswers[currentQuestionIndex] = optionIndex;

            // Update UI
            document.querySelectorAll('.option-item').forEach((item, index) => {
                item.classList.toggle('selected', index === optionIndex);
                const radio = item.querySelector('input[type="radio"]');
                radio.checked = index === optionIndex;
            });

            // Auto-save answer (in real app, this would save to server)
            saveAnswerLocally();
        }

        // Save Answer Locally
        function saveAnswerLocally() {
            const examProgress = {
                studentId: document.getElementById('studentLoginId').value,
                examId: currentExam.id,
                answers: studentAnswers,
                currentQuestion: currentQuestionIndex,
                timeRemaining: timeRemaining
            };
            localStorage.setItem('examProgress', JSON.stringify(examProgress));
        }

        // Update Navigation Buttons
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const totalQuestions = currentExam.questions.length;

            // Previous button
            prevBtn.disabled = currentQuestionIndex === 0;

            // Next button
            nextBtn.disabled = currentQuestionIndex === totalQuestions - 1;

            // Update button text
            if (currentLanguage === 'ar') {
                document.getElementById('prevBtnText').textContent = 'السابق';
                document.getElementById('nextBtnText').textContent = currentQuestionIndex === totalQuestions - 1 ? 'انتهى' : 'التالي';
            } else {
                document.getElementById('prevBtnText').textContent = 'Previous';
                document.getElementById('nextBtnText').textContent = currentQuestionIndex === totalQuestions - 1 ? 'Finish' : 'Next';
            }
        }

        // Previous Question
        function previousQuestion() {
            if (currentQuestionIndex > 0 && !isExamSubmitted) {
                currentQuestionIndex--;
                displayCurrentQuestion();
            }
        }

        // Next Question
        function nextQuestion() {
            if (currentQuestionIndex < currentExam.questions.length - 1 && !isExamSubmitted) {
                currentQuestionIndex++;
                displayCurrentQuestion();
            }
        }

        // Submit Exam
        function submitExam() {
            if (isExamSubmitted) return;

            const confirmMessage = currentLanguage === 'ar' ?
                'هل أنت متأكد من تسليم الامتحان؟ لن تتمكن من تعديل إجاباتك بعد التسليم.' :
                'Are you sure you want to submit the exam? You won\'t be able to modify your answers after submission.';

            if (!confirm(confirmMessage)) {
                return;
            }

            finishExam();
        }

        // Auto Submit Exam (when time expires)
        function autoSubmitExam() {
            const message = currentLanguage === 'ar' ?
                'انتهى الوقت المحدد للامتحان. سيتم تسليم الامتحان تلقائياً.' :
                'Time is up! The exam will be submitted automatically.';

            alert(message);
            finishExam();
        }

        // Finish Exam
        function finishExam() {
            isExamSubmitted = true;

            // Stop timer
            if (examTimer) {
                clearInterval(examTimer);
                examTimer = null;
            }

            // Disable all interactive elements
            disableExamInterface();

            // Calculate score
            const score = calculateScore();

            // Save result
            saveExamResult(score);

            // Show submission overlay
            showSubmissionModal(score);

            // Remove page leave prevention
            window.removeEventListener('beforeunload', preventPageLeave);

            // Clear local storage
            localStorage.removeItem('examProgress');
        }

        // Disable Exam Interface
        function disableExamInterface() {
            const examInterface = document.getElementById('examInterface');
            examInterface.classList.add('exam-disabled');

            // Disable all buttons and inputs
            const buttons = examInterface.querySelectorAll('button');
            const inputs = examInterface.querySelectorAll('input');

            buttons.forEach(btn => btn.disabled = true);
            inputs.forEach(input => input.disabled = true);

            // Remove click handlers
            document.querySelectorAll('.option-item').forEach(item => {
                item.onclick = null;
                item.style.cursor = 'not-allowed';
            });
        }

        // Calculate Score
        function calculateScore() {
            let correctAnswers = 0;
            const totalQuestions = currentExam.questions.length;

            currentExam.questions.forEach((question, index) => {
                if (question.type === 'mcq') {
                    const studentAnswer = studentAnswers[index];
                    const correctAnswer = question.correctAnswer - 1; // Convert to 0-based index

                    if (studentAnswer === correctAnswer) {
                        correctAnswers++;
                    }
                }
            });

            return {
                correct: correctAnswers,
                total: totalQuestions,
                percentage: Math.round((correctAnswers / totalQuestions) * 100)
            };
        }

        // Save Exam Result
        function saveExamResult(score) {
            const studentId = document.getElementById('studentLoginId').value;
            const endTime = new Date();
            const duration = Math.round((endTime - examStartTime) / 1000 / 60); // in minutes

            const result = {
                studentId: studentId,
                studentName: examData.students.find(s => s.id === studentId)?.name || 'Unknown',
                score: score.correct,
                totalQuestions: score.total,
                percentage: score.percentage,
                duration: duration,
                submissionTime: endTime.toLocaleString(),
                answers: [...studentAnswers]
            };

            // In real application, this would be sent to server
            if (!examData.results) {
                examData.results = [];
            }
            examData.results.push(result);

            // Update localStorage
            localStorage.setItem(`exam_${examData.id}`, JSON.stringify(examData));
        }

        // Show Submission Modal
        function showSubmissionModal(score) {
            const overlay = document.getElementById('submissionOverlay');
            const title = document.getElementById('submissionTitle');
            const message = document.getElementById('submissionMessage');

            if (currentLanguage === 'ar') {
                title.textContent = 'تم تسليم الامتحان بنجاح';
                message.textContent = `شكراً لك، حصلت على ${score.correct} من ${score.total} (${score.percentage}%)`;
            } else {
                title.textContent = 'Exam Submitted Successfully';
                message.textContent = `Thank you, you scored ${score.correct} out of ${score.total} (${score.percentage}%)`;
            }

            overlay.classList.remove('hidden');
        }

        // Close Submission Modal
        function closeSubmissionModal() {
            document.getElementById('submissionOverlay').classList.add('hidden');
        }

        // Prevent Page Leave
        function preventPageLeave(e) {
            if (!isExamSubmitted) {
                e.preventDefault();
                e.returnValue = '';
                return '';
            }
        }

        // Keyboard Navigation
        document.addEventListener('keydown', function(e) {
            if (!currentExam || isExamSubmitted) return;

            switch(e.key) {
                case 'ArrowLeft':
                    if (currentLanguage === 'ar') {
                        nextQuestion();
                    } else {
                        previousQuestion();
                    }
                    break;
                case 'ArrowRight':
                    if (currentLanguage === 'ar') {
                        previousQuestion();
                    } else {
                        nextQuestion();
                    }
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                    const optionIndex = parseInt(e.key) - 1;
                    if (optionIndex >= 0 && optionIndex < 4) {
                        selectOption(optionIndex);
                    }
                    break;
                case 'Enter':
                    if (e.ctrlKey) {
                        submitExam();
                    }
                    break;
            }
        });

        // Initialize the application when page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
