<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة الامتحانات الإلكترونية - جامعة السودان للعلوم والتكنولوجيا</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #1e3a8a;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        body.en {
            font-family: 'Inter', 'Cairo', sans-serif;
            direction: ltr;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: var(--white);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .language-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .language-toggle:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* Author Info */
        .author-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .author-info .author-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .author-info .contact {
            opacity: 0.9;
            margin: 2px 0;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            background: var(--white);
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--text-light);
        }

        .nav-tab.active {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .nav-tab:hover:not(.active) {
            background: var(--light-bg);
            color: var(--primary-color);
        }

        /* Main Content */
        .view {
            display: none;
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            box-shadow: var(--shadow-lg);
            animation: fadeIn 0.5s ease;
        }

        .view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Form Styles */
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            background: var(--light-bg);
        }

        .form-section h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--text-dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Button Styles */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background: var(--text-light);
            color: white;
        }

        /* Question Types */
        .question-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .question-type-card {
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: var(--white);
        }

        .question-type-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .question-type-card.selected {
            border-color: var(--primary-color);
            background: rgba(44, 90, 160, 0.1);
        }

        .question-type-card i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .question-type-selector {
                grid-template-columns: 1fr;
            }
        }

        /* Additional Styles */
        .exam-link-container {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }

        .exam-link-container input {
            flex: 1;
            max-width: 400px;
        }

        .btn-lg {
            padding: 15px 30px;
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .results-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-info p {
            color: var(--text-light);
            margin: 0;
        }

        .results-table-container {
            background: var(--white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .results-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .results-table tbody tr:hover {
            background: var(--light-bg);
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-left { text-align: left; }
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }
        .mt-20 { margin-top: 20px; }
        .hidden { display: none; }
        .flex { display: flex; }
        .flex-center { display: flex; align-items: center; justify-content: center; }
        .gap-10 { gap: 10px; }
        .gap-15 { gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="language-toggle" onclick="toggleLanguage()">
                <i class="fas fa-globe"></i>
                <span id="langText">English</span>
            </button>
            
            <h1 id="mainTitle">منصة الامتحانات الإلكترونية</h1>
            <p class="subtitle" id="subtitle">جامعة السودان للعلوم والتكنولوجيا</p>
        </div>

        <!-- Author Information -->
        <div class="author-info">
            <div class="author-name" id="authorName">د. محمد يعقوب إسماعيل</div>
            <div class="contact" id="authorTitle">جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية</div>
            <div class="contact">© 2025 | <EMAIL></div>
            <div class="contact">+249912867327 | +966538076790</div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showView('teacher')" id="teacherTab">
                <i class="fas fa-chalkboard-teacher"></i>
                <span id="teacherTabText">إعداد الامتحان</span>
            </button>
            <button class="nav-tab" onclick="showView('student')" id="studentTab">
                <i class="fas fa-user-graduate"></i>
                <span id="studentTabText">امتحان الطالب</span>
            </button>
            <button class="nav-tab" onclick="showView('results')" id="resultsTab">
                <i class="fas fa-chart-bar"></i>
                <span id="resultsTabText">النتائج</span>
            </button>
        </div>

        <!-- Teacher's Setup View -->
        <div id="teacherView" class="view active">
            <!-- Academic Information Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-university"></i>
                    <span id="academicInfoTitle">المعلومات الأكاديمية</span>
                </h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="college" id="collegeLabel">الكلية</label>
                        <select id="college" onchange="updateMajors()">
                            <option value="" id="selectCollegeOption">اختر الكلية</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="major" id="majorLabel">التخصص</label>
                        <select id="major" disabled>
                            <option value="" id="selectMajorOption">اختر التخصص أولاً</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="academicYear" id="academicYearLabel">السنة الدراسية</label>
                        <select id="academicYear" onchange="updateSpecializations()">
                            <option value="" id="selectYearOption">اختر السنة الدراسية</option>
                            <option value="1">السنة الأولى</option>
                            <option value="2">السنة الثانية</option>
                            <option value="3">السنة الثالثة</option>
                            <option value="4">السنة الرابعة</option>
                            <option value="5">السنة الخامسة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="semester" id="semesterLabel">الفصل الدراسي</label>
                        <select id="semester" onchange="updateSpecializations()">
                            <option value="" id="selectSemesterOption">اختر الفصل الدراسي</option>
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="courseName" id="courseNameLabel">اسم المقرر</label>
                        <input type="text" id="courseName" placeholder="أدخل اسم المقرر">
                    </div>

                    <div class="form-group">
                        <label for="examTitle" id="examTitleLabel">عنوان الامتحان</label>
                        <input type="text" id="examTitle" placeholder="أدخل عنوان الامتحان">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="examDate" id="examDateLabel">تاريخ الامتحان</label>
                        <input type="datetime-local" id="examDate">
                    </div>

                    <div class="form-group">
                        <label for="examDuration" id="examDurationLabel">مدة الامتحان (بالدقائق)</label>
                        <input type="number" id="examDuration" min="1" max="300" placeholder="60">
                    </div>
                </div>
            </div>

            <!-- Student Management Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-users"></i>
                    <span id="studentMgmtTitle">إدارة الطلاب</span>
                </h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="studentId" id="studentIdLabel">رقم الطالب</label>
                        <input type="text" id="studentId" placeholder="أدخل رقم الطالب">
                    </div>

                    <div class="form-group">
                        <label for="studentName" id="studentNameLabel">اسم الطالب</label>
                        <input type="text" id="studentName" placeholder="أدخل اسم الطالب">
                    </div>

                    <div class="form-group flex-center">
                        <button class="btn btn-primary" onclick="addStudent()" id="addStudentBtn">
                            <i class="fas fa-plus"></i>
                            <span>إضافة طالب</span>
                        </button>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="csvFile" id="csvFileLabel">رفع ملف CSV للطلاب</label>
                        <input type="file" id="csvFile" accept=".csv" onchange="handleCSVUpload(event)">
                    </div>
                </div>

                <div id="studentsList" class="mt-20">
                    <h4 id="studentsListTitle">قائمة الطلاب المسجلين:</h4>
                    <div id="studentsContainer"></div>
                </div>
            </div>

            <!-- Question Types Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-question-circle"></i>
                    <span id="questionTypesTitle">أنواع الأسئلة</span>
                </h3>

                <div class="question-type-selector">
                    <div class="question-type-card" onclick="selectQuestionType('mcq')" data-type="mcq">
                        <i class="fas fa-list-ul"></i>
                        <h4 id="mcqTitle">أسئلة الاختيار من متعدد</h4>
                        <p id="mcqDesc">MCQ Questions</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('short')" data-type="short">
                        <i class="fas fa-edit"></i>
                        <h4 id="shortTitle">أسئلة قصيرة</h4>
                        <p id="shortDesc">Short Answer Questions</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('fillblank')" data-type="fillblank">
                        <i class="fas fa-fill-drip"></i>
                        <h4 id="fillBlankTitle">ملء الفراغات</h4>
                        <p id="fillBlankDesc">Fill in the Blanks</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('diagram')" data-type="diagram">
                        <i class="fas fa-project-diagram"></i>
                        <h4 id="diagramTitle">تسمية الأجزاء</h4>
                        <p id="diagramDesc">Name Parts in Figures</p>
                    </div>

                    <div class="question-type-card" onclick="selectQuestionType('flowchart')" data-type="flowchart">
                        <i class="fas fa-sitemap"></i>
                        <h4 id="flowchartTitle">المخططات الانسيابية</h4>
                        <p id="flowchartDesc">Flowchart Questions</p>
                    </div>
                </div>
            </div>

            <!-- Question Creation Section -->
            <div class="form-section" id="questionCreationSection" style="display: none;">
                <h3>
                    <i class="fas fa-plus-circle"></i>
                    <span id="createQuestionTitle">إنشاء الأسئلة</span>
                </h3>

                <div id="questionForm">
                    <!-- Dynamic question form will be inserted here -->
                </div>

                <div class="text-center mt-20">
                    <button type="button" class="btn btn-success" onclick="addQuestion()" id="addQuestionBtn">
                        <i class="fas fa-plus"></i>
                        <span>إضافة السؤال</span>
                    </button>
                </div>
            </div>

            <!-- Questions List -->
            <div class="form-section" id="questionsListSection" style="display: none;">
                <h3>
                    <i class="fas fa-list"></i>
                    <span id="questionsListTitle">قائمة الأسئلة</span>
                </h3>

                <div id="questionsList"></div>
            </div>

            <!-- Generate Exam Section -->
            <div class="form-section">
                <h3>
                    <i class="fas fa-link"></i>
                    <span id="generateExamTitle">إنشاء رابط الامتحان</span>
                </h3>

                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg" onclick="generateExam()" id="generateExamBtn">
                        <i class="fas fa-rocket"></i>
                        <span>إنشاء الامتحان</span>
                    </button>
                </div>

                <div id="examLinkSection" class="mt-20 text-center" style="display: none;">
                    <h4 id="examLinkTitle">رابط الامتحان:</h4>
                    <div class="exam-link-container">
                        <input type="text" id="examLink" readonly class="form-control">
                        <button type="button" class="btn btn-secondary" onclick="copyExamLink()" id="copyLinkBtn">
                            <i class="fas fa-copy"></i>
                            <span>نسخ الرابط</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student's Exam View -->
        <div id="studentView" class="view">
            <div class="text-center mb-20">
                <h2 id="studentWelcomeTitle">مرحباً بك في الامتحان</h2>
                <p id="studentWelcomeDesc">يرجى إدخال رقم الطالب للبدء</p>
            </div>

            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="studentLoginId" id="studentLoginLabel">رقم الطالب</label>
                        <input type="text" id="studentLoginId" placeholder="أدخل رقم الطالب">
                    </div>

                    <div class="form-group flex-center">
                        <button type="button" class="btn btn-primary" onclick="startExam()" id="startExamBtn">
                            <i class="fas fa-play"></i>
                            <span>بدء الامتحان</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher's Results View -->
        <div id="resultsView" class="view">
            <div class="text-center mb-20">
                <h2 id="resultsTitle">نتائج الامتحان</h2>
                <p id="resultsDesc">عرض وتحليل نتائج الطلاب</p>
            </div>

            <div id="resultsContent">
                <p id="noResultsMsg">لا توجد نتائج متاحة حالياً</p>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let currentLanguage = 'ar';
        let sustData = {};
        let students = [];
        let questions = [];
        let currentQuestionType = '';
        let examData = {};

        // Language Data
        const translations = {
            ar: {
                mainTitle: "منصة الامتحانات الإلكترونية",
                subtitle: "جامعة السودان للعلوم والتكنولوجيا",
                authorName: "د. محمد يعقوب إسماعيل",
                authorTitle: "جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية",
                teacherTabText: "إعداد الامتحان",
                studentTabText: "امتحان الطالب",
                resultsTabText: "النتائج",
                academicInfoTitle: "المعلومات الأكاديمية",
                collegeLabel: "الكلية",
                majorLabel: "التخصص",
                academicYearLabel: "السنة الدراسية",
                semesterLabel: "الفصل الدراسي",
                courseNameLabel: "اسم المقرر",
                examTitleLabel: "عنوان الامتحان",
                examDateLabel: "تاريخ الامتحان",
                examDurationLabel: "مدة الامتحان (بالدقائق)",
                studentMgmtTitle: "إدارة الطلاب",
                studentIdLabel: "رقم الطالب",
                studentNameLabel: "اسم الطالب",
                csvFileLabel: "رفع ملف CSV للطلاب",
                studentsListTitle: "قائمة الطلاب المسجلين:",
                questionTypesTitle: "أنواع الأسئلة",
                mcqTitle: "أسئلة الاختيار من متعدد",
                shortTitle: "أسئلة قصيرة",
                fillBlankTitle: "ملء الفراغات",
                diagramTitle: "تسمية الأجزاء",
                flowchartTitle: "المخططات الانسيابية",
                createQuestionTitle: "إنشاء الأسئلة",
                questionsListTitle: "قائمة الأسئلة",
                generateExamTitle: "إنشاء رابط الامتحان",
                examLinkTitle: "رابط الامتحان:",
                studentWelcomeTitle: "مرحباً بك في الامتحان",
                studentWelcomeDesc: "يرجى إدخال رقم الطالب للبدء",
                studentLoginLabel: "رقم الطالب",
                resultsTitle: "نتائج الامتحان",
                resultsDesc: "عرض وتحليل نتائج الطلاب",
                noResultsMsg: "لا توجد نتائج متاحة حالياً"
            },
            en: {
                mainTitle: "Online Exam Platform",
                subtitle: "Sudan University of Science and Technology",
                authorName: "Dr. Mohammed Yagoub Esmail",
                authorTitle: "Sudan University of Science and Technology - BME Department",
                teacherTabText: "Exam Setup",
                studentTabText: "Student Exam",
                resultsTabText: "Results",
                academicInfoTitle: "Academic Information",
                collegeLabel: "College",
                majorLabel: "Major/Specialization",
                academicYearLabel: "Academic Year",
                semesterLabel: "Semester",
                courseNameLabel: "Course Name",
                examTitleLabel: "Exam Title",
                examDateLabel: "Exam Date",
                examDurationLabel: "Exam Duration (minutes)",
                studentMgmtTitle: "Student Management",
                studentIdLabel: "Student ID",
                studentNameLabel: "Student Name",
                csvFileLabel: "Upload CSV File for Students",
                studentsListTitle: "Registered Students:",
                questionTypesTitle: "Question Types",
                mcqTitle: "Multiple Choice Questions",
                shortTitle: "Short Answer Questions",
                fillBlankTitle: "Fill in the Blanks",
                diagramTitle: "Name Parts in Figures",
                flowchartTitle: "Flowchart Questions",
                createQuestionTitle: "Create Questions",
                questionsListTitle: "Questions List",
                generateExamTitle: "Generate Exam Link",
                examLinkTitle: "Exam Link:",
                studentWelcomeTitle: "Welcome to the Exam",
                studentWelcomeDesc: "Please enter your student ID to begin",
                studentLoginLabel: "Student ID",
                resultsTitle: "Exam Results",
                resultsDesc: "View and analyze student results",
                noResultsMsg: "No results available currently"
            }
        };

        // Initialize SUST Data
        function initializeSustData() {
            sustData = {
                "كلية الهندسة": [
                    "مدرسة الهندسة المدنية - هندسة الإنشاءات",
                    "مدرسة الهندسة المدنية - هندسة الموارد المائية والبيئية",
                    "مدرسة الهندسة المدنية - هندسة النقل والطرق",
                    "مدرسة الهندسة المدنية - إدارة التشييد",
                    "مدرسة الهندسة الميكانيكية - هندسة القوى والحراريات",
                    "مدرسة الهندسة الميكانيكية - التصميم والإنتاج",
                    "مدرسة الهندسة الميكانيكية - الهندسة الصناعية",
                    "مدرسة الهندسة الميكانيكية - الميكاترونكس",
                    "مدرسة هندسة الكهرباء - هندسة القدرة والآلات الكهربائية",
                    "مدرسة هندسة الكهرباء - هندسة التحكم",
                    "مدرسة هندسة المساحة - الجيوديسيا",
                    "مدرسة هندسة المساحة - التصوير المساحي والاستشعار عن بعد",
                    "مدرسة هندسة المساحة - نظم المعلومات الجغرافية",
                    "مدرسة هندسة الإلكترونيات - هندسة الاتصالات",
                    "مدرسة هندسة الإلكترونيات - الأنظمة المدمجة",
                    "مدرسة هندسة الإلكترونيات - هندسة الحاسوب والشبكات",
                    "قسم الهندسة الطبية الحيوية",
                    "قسم هندسة الطيران"
                ],
                "كلية هندسة العمارة والتخطيط العمراني": ["هندسة العمارة", "التخطيط العمراني"],
                "كلية الهندسة الصناعية الكيميائية": ["الهندسة الكيميائية", "هندسة العمليات الصناعية", "الصناعات البتروكيماوية", "الصناعات الدوائية", "الصناعات الغذائية"],
                "كلية هندسة النفط": ["هندسة استكشاف النفط", "هندسة استخراج النفط", "هندسة إنتاج النفط والغاز"],
                "كلية هندسة المياه (ود المقبول)": ["هندسة الموارد المائية", "هندسة معالجة المياه", "هندسة الري والصرف"],
                "كلية الطب البشري": ["الطب العام", "الجراحة العامة", "الطب الباطني", "طب الأطفال", "النساء والتوليد", "طب المجتمع"],
                "كلية الصيدلة": ["الصيدلة الإكلينيكية", "الكيمياء الصيدلانية", "علم الأدوية", "الصيدلانيات"],
                "كلية طب الأسنان": ["طب الأسنان العام", "جراحة الفم والأسنان", "تقويم الأسنان", "طب أسنان الأطفال"],
                "كلية علوم الحاسوب وتقنية المعلومات": ["علوم الحاسوب", "تقنية المعلومات", "نظم المعلومات"],
                "كلية العلوم": ["الفيزياء", "الكيمياء", "الرياضيات", "علوم الأرض (الجيولوجيا)"],
                "كلية الدراسات التجارية": ["المحاسبة", "إدارة الأعمال", "التسويق", "التمويل والمصارف"]
            };
        }

        // Initialize the application
        function initializeApp() {
            initializeSustData();
            populateColleges();
            updateLanguage();
        }

        // Language Toggle Function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage();
        }

        // Update Language
        function updateLanguage() {
            const body = document.body;
            const langText = document.getElementById('langText');

            if (currentLanguage === 'en') {
                body.classList.add('en');
                body.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', 'en');
                langText.textContent = 'العربية';
            } else {
                body.classList.remove('en');
                body.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
                langText.textContent = 'English';
            }

            // Update all text elements
            const texts = translations[currentLanguage];
            Object.keys(texts).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = texts[key];
                }
            });
        }

        // Populate Colleges
        function populateColleges() {
            const collegeSelect = document.getElementById('college');
            collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';

            Object.keys(sustData).forEach(college => {
                const option = document.createElement('option');
                option.value = college;
                option.textContent = college;
                collegeSelect.appendChild(option);
            });
        }

        // Update Majors based on College selection
        function updateMajors() {
            const collegeSelect = document.getElementById('college');
            const majorSelect = document.getElementById('major');
            const selectedCollege = collegeSelect.value;

            majorSelect.innerHTML = '<option value="">اختر التخصص</option>';
            majorSelect.disabled = !selectedCollege;

            if (selectedCollege && sustData[selectedCollege]) {
                sustData[selectedCollege].forEach(major => {
                    const option = document.createElement('option');
                    option.value = major;
                    option.textContent = major;
                    majorSelect.appendChild(option);
                });
                majorSelect.disabled = false;
            }
        }

        // Update Specializations based on Year and Semester
        function updateSpecializations() {
            const year = document.getElementById('academicYear').value;
            const semester = document.getElementById('semester').value;
            const majorSelect = document.getElementById('major');

            // Logic for engineering specializations starting from 4th year, 8th semester
            if (year && semester) {
                const semesterNumber = (parseInt(year) - 1) * 2 + parseInt(semester);

                // If semester 8 or higher, show detailed specializations
                if (semesterNumber >= 8) {
                    updateMajors(); // Keep current detailed specializations
                } else {
                    // Show general school names only
                    updateMajorsForEarlyYears();
                }
            }
        }

        // Update majors for early years (general school names only)
        function updateMajorsForEarlyYears() {
            const collegeSelect = document.getElementById('college');
            const majorSelect = document.getElementById('major');
            const selectedCollege = collegeSelect.value;

            if (selectedCollege === "كلية الهندسة") {
                majorSelect.innerHTML = '<option value="">اختر التخصص</option>';
                const generalMajors = [
                    "مدرسة الهندسة المدنية",
                    "مدرسة الهندسة الميكانيكية",
                    "مدرسة هندسة الكهرباء",
                    "مدرسة هندسة المساحة",
                    "مدرسة هندسة الإلكترونيات",
                    "قسم الهندسة الطبية الحيوية",
                    "قسم هندسة الطيران"
                ];

                generalMajors.forEach(major => {
                    const option = document.createElement('option');
                    option.value = major;
                    option.textContent = major;
                    majorSelect.appendChild(option);
                });
            } else {
                updateMajors(); // For non-engineering colleges, use normal majors
            }
        }

        // Show View Function
        function showView(viewName) {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected view
            document.getElementById(viewName + 'View').classList.add('active');

            // Add active class to selected tab
            document.getElementById(viewName + 'Tab').classList.add('active');
        }

        // Add Student Function
        function addStudent() {
            const studentId = document.getElementById('studentId').value.trim();
            const studentName = document.getElementById('studentName').value.trim();

            if (!studentId || !studentName) {
                alert('يرجى إدخال رقم الطالب والاسم');
                return;
            }

            // Check if student already exists
            if (students.find(s => s.id === studentId)) {
                alert('هذا الطالب مسجل مسبقاً');
                return;
            }

            students.push({ id: studentId, name: studentName });
            updateStudentsList();

            // Clear inputs
            document.getElementById('studentId').value = '';
            document.getElementById('studentName').value = '';
        }

        // Update Students List
        function updateStudentsList() {
            const container = document.getElementById('studentsContainer');
            container.innerHTML = '';

            students.forEach((student, index) => {
                const studentDiv = document.createElement('div');
                studentDiv.className = 'student-item';
                studentDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px;';
                studentDiv.innerHTML = `
                    <span>${student.id} - ${student.name}</span>
                    <button type="button" class="btn btn-danger" style="padding: 5px 10px; font-size: 0.8rem;" onclick="removeStudent(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(studentDiv);
            });
        }

        // Remove Student Function
        function removeStudent(index) {
            students.splice(index, 1);
            updateStudentsList();
        }

        // Handle CSV Upload
        function handleCSVUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n').filter(line => line.trim());
                let addedCount = 0;

                lines.forEach(line => {
                    const [id, name] = line.split(',').map(item => item.trim());
                    if (id && name && !students.find(s => s.id === id)) {
                        students.push({ id, name });
                        addedCount++;
                    }
                });

                updateStudentsList();
                alert(`تم إضافة ${addedCount} طالب من الملف`);
            };

            reader.readAsText(file);
        }

        // Initialize the application when page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
