/* SUST Online Exam Platform - Enhanced Styles */
/* Author: Dr. <PERSON>, SUST-BME, © 2025 */

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c5aa0;
    --secondary-color: #1e3a8a;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Cairo', 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

body.en {
    font-family: 'Inter', 'Cairo', sans-serif;
    direction: ltr;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-lg);
    text-align: center;
    position: relative;
}

.header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.header .subtitle {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.language-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.language-toggle:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Author Info */
.author-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 0.9rem;
}

.author-info .author-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.author-info .contact {
    opacity: 0.9;
    margin: 2px 0;
}

/* Navigation */
.nav-tabs {
    display: flex;
    background: var(--white);
    border-radius: 15px;
    padding: 5px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.nav-tab {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    color: var(--text-light);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.nav-tab:hover:not(.active) {
    background: var(--light-bg);
    color: var(--primary-color);
}

/* Main Content */
.view {
    display: none;
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow-lg);
    animation: fadeIn 0.5s ease;
}

.view.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Styles */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--light-bg);
}

.form-section h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-secondary {
    background: var(--text-light);
    color: white;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Question Types */
.question-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.question-type-card {
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: var(--white);
}

.question-type-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.question-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(44, 90, 160, 0.1);
}

.question-type-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Exam Link Container */
.exam-link-container {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.exam-link-container input {
    flex: 1;
    max-width: 400px;
}

/* Results Styles */
.results-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-info p {
    color: var(--text-light);
    margin: 0;
}

.results-table-container {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th,
.results-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.results-table tbody tr:hover {
    background: var(--light-bg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .question-type-selector {
        grid-template-columns: 1fr;
    }

    .language-toggle {
        position: static;
        margin-bottom: 15px;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.gap-10 { gap: 10px; }
.gap-15 { gap: 15px; }
