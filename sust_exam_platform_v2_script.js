/**
 * SUST Online Exam Platform - Enhanced JavaScript
 * Author: Dr. <PERSON>, SUST-BME, © 2025
 * Email: <EMAIL>
 * Phone: +249912867327, +966538076790
 */

// Global Variables
let currentLanguage = 'ar';
let sustData = {};
let students = [];
let questions = [];
let currentQuestionType = '';
let examData = {};

// Language Data
const translations = {
    ar: {
        mainTitle: "منصة الامتحانات الإلكترونية",
        subtitle: "جامعة السودان للعلوم والتكنولوجيا",
        authorName: "د. محمد يعقوب إسماعيل",
        authorTitle: "جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية",
        teacherTabText: "إعداد الامتحان",
        studentTabText: "امتحان الطالب",
        resultsTabText: "النتائج",
        academicInfoTitle: "المعلومات الأكاديمية",
        collegeLabel: "الكلية",
        majorLabel: "التخصص",
        academicYearLabel: "السنة الدراسية",
        semesterLabel: "الفصل الدراسي",
        courseNameLabel: "اسم المقرر",
        examTitleLabel: "عنوان الامتحان",
        examDateLabel: "تاريخ الامتحان",
        examDurationLabel: "مدة الامتحان (بالدقائق)",
        studentMgmtTitle: "إدارة الطلاب",
        studentIdLabel: "رقم الطالب",
        studentNameLabel: "اسم الطالب",
        csvFileLabel: "رفع ملف CSV للطلاب",
        studentsListTitle: "قائمة الطلاب المسجلين:",
        questionTypesTitle: "أنواع الأسئلة",
        mcqTitle: "أسئلة الاختيار من متعدد",
        shortTitle: "أسئلة قصيرة",
        fillBlankTitle: "ملء الفراغات",
        diagramTitle: "تسمية الأجزاء",
        flowchartTitle: "المخططات الانسيابية",
        createQuestionTitle: "إنشاء الأسئلة",
        questionsListTitle: "قائمة الأسئلة",
        generateExamTitle: "إنشاء رابط الامتحان",
        examLinkTitle: "رابط الامتحان:",
        studentWelcomeTitle: "مرحباً بك في الامتحان",
        studentWelcomeDesc: "يرجى إدخال رقم الطالب للبدء",
        studentLoginLabel: "رقم الطالب",
        resultsTitle: "نتائج الامتحان",
        resultsDesc: "عرض وتحليل نتائج الطلاب",
        noResultsMsg: "لا توجد نتائج متاحة حالياً",
        selectCollegeOption: "اختر الكلية",
        selectMajorOption: "اختر التخصص أولاً",
        selectYearOption: "اختر السنة الدراسية",
        selectSemesterOption: "اختر الفصل الدراسي"
    },
    en: {
        mainTitle: "Online Exam Platform",
        subtitle: "Sudan University of Science and Technology",
        authorName: "Dr. Mohammed Yagoub Esmail",
        authorTitle: "Sudan University of Science and Technology - BME Department",
        teacherTabText: "Exam Setup",
        studentTabText: "Student Exam",
        resultsTabText: "Results",
        academicInfoTitle: "Academic Information",
        collegeLabel: "College",
        majorLabel: "Major/Specialization",
        academicYearLabel: "Academic Year",
        semesterLabel: "Semester",
        courseNameLabel: "Course Name",
        examTitleLabel: "Exam Title",
        examDateLabel: "Exam Date",
        examDurationLabel: "Exam Duration (minutes)",
        studentMgmtTitle: "Student Management",
        studentIdLabel: "Student ID",
        studentNameLabel: "Student Name",
        csvFileLabel: "Upload CSV File for Students",
        studentsListTitle: "Registered Students:",
        questionTypesTitle: "Question Types",
        mcqTitle: "Multiple Choice Questions",
        shortTitle: "Short Answer Questions",
        fillBlankTitle: "Fill in the Blanks",
        diagramTitle: "Name Parts in Figures",
        flowchartTitle: "Flowchart Questions",
        createQuestionTitle: "Create Questions",
        questionsListTitle: "Questions List",
        generateExamTitle: "Generate Exam Link",
        examLinkTitle: "Exam Link:",
        studentWelcomeTitle: "Welcome to the Exam",
        studentWelcomeDesc: "Please enter your student ID to begin",
        studentLoginLabel: "Student ID",
        resultsTitle: "Exam Results",
        resultsDesc: "View and analyze student results",
        noResultsMsg: "No results available currently",
        selectCollegeOption: "Select College",
        selectMajorOption: "Select Major First",
        selectYearOption: "Select Academic Year",
        selectSemesterOption: "Select Semester"
    }
};

// Initialize SUST Data
function initializeSustData() {
    sustData = {
        "كلية الهندسة": [
            "مدرسة الهندسة المدنية - هندسة الإنشاءات", 
            "مدرسة الهندسة المدنية - هندسة الموارد المائية والبيئية", 
            "مدرسة الهندسة المدنية - هندسة النقل والطرق", 
            "مدرسة الهندسة المدنية - إدارة التشييد",
            "مدرسة الهندسة الميكانيكية - هندسة القوى والحراريات", 
            "مدرسة الهندسة الميكانيكية - التصميم والإنتاج", 
            "مدرسة الهندسة الميكانيكية - الهندسة الصناعية", 
            "مدرسة الهندسة الميكانيكية - الميكاترونكس",
            "مدرسة هندسة الكهرباء - هندسة القدرة والآلات الكهربائية", 
            "مدرسة هندسة الكهرباء - هندسة التحكم", 
            "مدرسة هندسة المساحة - الجيوديسيا", 
            "مدرسة هندسة المساحة - التصوير المساحي والاستشعار عن بعد", 
            "مدرسة هندسة المساحة - نظم المعلومات الجغرافية", 
            "مدرسة هندسة الإلكترونيات - هندسة الاتصالات", 
            "مدرسة هندسة الإلكترونيات - الأنظمة المدمجة", 
            "مدرسة هندسة الإلكترونيات - هندسة الحاسوب والشبكات", 
            "قسم الهندسة الطبية الحيوية", 
            "قسم هندسة الطيران"
        ],
        "كلية هندسة العمارة والتخطيط العمراني": ["هندسة العمارة", "التخطيط العمراني"],
        "كلية الهندسة الصناعية الكيميائية": ["الهندسة الكيميائية", "هندسة العمليات الصناعية", "الصناعات البتروكيماوية", "الصناعات الدوائية", "الصناعات الغذائية"],
        "كلية هندسة النفط": ["هندسة استكشاف النفط", "هندسة استخراج النفط", "هندسة إنتاج النفط والغاز"],
        "كلية هندسة المياه (ود المقبول)": ["هندسة الموارد المائية", "هندسة معالجة المياه", "هندسة الري والصرف"],
        "كلية الطب البشري": ["الطب العام", "الجراحة العامة", "الطب الباطني", "طب الأطفال", "النساء والتوليد", "طب المجتمع"],
        "كلية الصيدلة": ["الصيدلة الإكلينيكية", "الكيمياء الصيدلانية", "علم الأدوية", "الصيدلانيات"],
        "كلية طب الأسنان": ["طب الأسنان العام", "جراحة الفم والأسنان", "تقويم الأسنان", "طب أسنان الأطفال"],
        "كلية علوم الحاسوب وتقنية المعلومات": ["علوم الحاسوب", "تقنية المعلومات", "نظم المعلومات"],
        "كلية العلوم": ["الفيزياء", "الكيمياء", "الرياضيات", "علوم الأرض (الجيولوجيا)"],
        "كلية الدراسات التجارية": ["المحاسبة", "إدارة الأعمال", "التسويق", "التمويل والمصارف"],
        "كلية اللغات": ["اللغة الإنجليزية", "اللغة الفرنسية", "اللغة العربية", "اللغة الألمانية"],
        "كلية الفنون الجميلة والتطبيقية": ["التصميم الصناعي", "التصميم الإيضاحي", "الطباعة والنشر", "الخزف", "النسيج", "الرسم والتصوير", "النحت"],
        "كلية التربية": ["التربية التقنية", "التربية الأسرية", "التربية الفنية", "علم النفس التربوي", "المناهج وطرق التدريس"],
        "كلية التربية البدنية": ["التربية البدنية والرياضة", "علوم الحركة", "الإدارة الرياضية", "التدريب الرياضي"],
        "كلية الموسيقى والدراما": ["الموسيقى", "الدراما", "الفنون المسرحية", "التأليف الموسيقي"],
        "كلية الإنتاج الحيواني": ["الإنتاج الحيواني", "علوم الألبان", "تربية الدواجن", "التغذية الحيوانية"],
        "كلية علوم الأشعة الطبية": ["التصوير الطبي", "الأشعة التشخيصية", "الطب النووي", "العلاج الإشعاعي"],
        "كلية المختبرات الطبية": ["علم الأمراض", "الكيمياء الحيوية الطبية", "علم الأحياء الدقيقة الطبية", "علم أمراض الدم"],
        "كلية تكنولوجيا النفط والغاز": ["تكنولوجيا تشغيل المصافي", "تكنولوجيا إنتاج النفط والغاز"],
        "كلية علوم الاتصال": ["العلاقات العامة والإعلان", "الصحافة والنشر الإلكتروني", "الإذاعة والتلفزيون"],
        "كلية الزراعة": ["الإنتاج النباتي", "علوم وتكنولوجيا الأغذية", "الهندسة الزراعية"],
        "كلية الطب البيطري": ["الطب البيطري"],
        "كلية الغابات والمراعي": ["علوم الغابات", "علوم المراعي"],
        "معهد الليزر": ["تطبيقات الليزر الطبية", "تطبيقات الليزر الصناعية", "فيزياء الليزر", "تقنيات الليزر المتقدمة"]
    };
}

// Initialize the application
function initializeApp() {
    initializeSustData();
    populateColleges();
    updateLanguage();
}

// Language Toggle Function
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    updateLanguage();
}

// Update Language
function updateLanguage() {
    const body = document.body;
    const langText = document.getElementById('langText');
    
    if (currentLanguage === 'en') {
        body.classList.add('en');
        body.setAttribute('dir', 'ltr');
        document.documentElement.setAttribute('lang', 'en');
        langText.textContent = 'العربية';
    } else {
        body.classList.remove('en');
        body.setAttribute('dir', 'rtl');
        document.documentElement.setAttribute('lang', 'ar');
        langText.textContent = 'English';
    }

    // Update all text elements
    const texts = translations[currentLanguage];
    Object.keys(texts).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.textContent = texts[key];
        }
    });
}

// Populate Colleges
function populateColleges() {
    const collegeSelect = document.getElementById('college');
    const defaultText = currentLanguage === 'ar' ? 'اختر الكلية' : 'Select College';
    collegeSelect.innerHTML = `<option value="">${defaultText}</option>`;
    
    Object.keys(sustData).forEach(college => {
        const option = document.createElement('option');
        option.value = college;
        option.textContent = college;
        collegeSelect.appendChild(option);
    });
}

// Update Majors based on College selection
function updateMajors() {
    const collegeSelect = document.getElementById('college');
    const majorSelect = document.getElementById('major');
    const selectedCollege = collegeSelect.value;
    const defaultText = currentLanguage === 'ar' ? 'اختر التخصص' : 'Select Major';
    
    majorSelect.innerHTML = `<option value="">${defaultText}</option>`;
    majorSelect.disabled = !selectedCollege;
    
    if (selectedCollege && sustData[selectedCollege]) {
        sustData[selectedCollege].forEach(major => {
            const option = document.createElement('option');
            option.value = major;
            option.textContent = major;
            majorSelect.appendChild(option);
        });
        majorSelect.disabled = false;
    }
}

// Update Specializations based on Year and Semester
function updateSpecializations() {
    const year = document.getElementById('academicYear').value;
    const semester = document.getElementById('semester').value;
    const majorSelect = document.getElementById('major');
    
    // Logic for engineering specializations starting from 4th year, 8th semester
    if (year && semester) {
        const semesterNumber = (parseInt(year) - 1) * 2 + parseInt(semester);
        
        // If semester 8 or higher, show detailed specializations
        if (semesterNumber >= 8) {
            updateMajors(); // Keep current detailed specializations
        } else {
            // Show general school names only
            updateMajorsForEarlyYears();
        }
    }
}

// Update majors for early years (general school names only)
function updateMajorsForEarlyYears() {
    const collegeSelect = document.getElementById('college');
    const majorSelect = document.getElementById('major');
    const selectedCollege = collegeSelect.value;
    const defaultText = currentLanguage === 'ar' ? 'اختر التخصص' : 'Select Major';
    
    if (selectedCollege === "كلية الهندسة") {
        majorSelect.innerHTML = `<option value="">${defaultText}</option>`;
        const generalMajors = [
            "مدرسة الهندسة المدنية",
            "مدرسة الهندسة الميكانيكية",
            "مدرسة هندسة الكهرباء",
            "مدرسة هندسة المساحة",
            "مدرسة هندسة الإلكترونيات",
            "قسم الهندسة الطبية الحيوية",
            "قسم هندسة الطيران"
        ];
        
        generalMajors.forEach(major => {
            const option = document.createElement('option');
            option.value = major;
            option.textContent = major;
            majorSelect.appendChild(option);
        });
    } else {
        updateMajors(); // For non-engineering colleges, use normal majors
    }
}

// Show View Function
function showView(viewName) {
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
        view.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected view
    document.getElementById(viewName + 'View').classList.add('active');
    
    // Add active class to selected tab
    document.getElementById(viewName + 'Tab').classList.add('active');
}

// Add Student Function
function addStudent() {
    const studentId = document.getElementById('studentId').value.trim();
    const studentName = document.getElementById('studentName').value.trim();
    
    if (!studentId || !studentName) {
        const message = currentLanguage === 'ar' ? 'يرجى إدخال رقم الطالب والاسم' : 'Please enter student ID and name';
        alert(message);
        return;
    }
    
    // Check if student already exists
    if (students.find(s => s.id === studentId)) {
        const message = currentLanguage === 'ar' ? 'هذا الطالب مسجل مسبقاً' : 'This student is already registered';
        alert(message);
        return;
    }
    
    students.push({ id: studentId, name: studentName });
    updateStudentsList();
    
    // Clear inputs
    document.getElementById('studentId').value = '';
    document.getElementById('studentName').value = '';
}

// Update Students List
function updateStudentsList() {
    const container = document.getElementById('studentsContainer');
    container.innerHTML = '';
    
    students.forEach((student, index) => {
        const studentDiv = document.createElement('div');
        studentDiv.className = 'student-item';
        studentDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px;';
        studentDiv.innerHTML = `
            <span>${student.id} - ${student.name}</span>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeStudent(${index})">
                <i class="fas fa-trash"></i>
            </button>
        `;
        container.appendChild(studentDiv);
    });
}

// Remove Student Function
function removeStudent(index) {
    students.splice(index, 1);
    updateStudentsList();
}

// Handle CSV Upload
function handleCSVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n').filter(line => line.trim());
        let addedCount = 0;
        
        lines.forEach(line => {
            const [id, name] = line.split(',').map(item => item.trim());
            if (id && name && !students.find(s => s.id === id)) {
                students.push({ id, name });
                addedCount++;
            }
        });
        
        updateStudentsList();
        const message = currentLanguage === 'ar' ? 
            `تم إضافة ${addedCount} طالب من الملف` : 
            `Added ${addedCount} students from file`;
        alert(message);
    };
    
    reader.readAsText(file);
}

// Select Question Type
function selectQuestionType(type) {
    currentQuestionType = type;
    
    // Update UI
    document.querySelectorAll('.question-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    document.querySelector(`[data-type="${type}"]`).classList.add('selected');
    
    // Show question creation section
    document.getElementById('questionCreationSection').style.display = 'block';
    
    // Generate question form based on type
    generateQuestionForm(type);
}

// Generate Question Form
function generateQuestionForm(type) {
    const formContainer = document.getElementById('questionForm');
    let formHTML = '';
    
    const isArabic = currentLanguage === 'ar';
    
    switch(type) {
        case 'mcq':
            formHTML = `
                <div class="form-group">
                    <label>${isArabic ? 'نص السؤال:' : 'Question Text:'}</label>
                    <textarea id="questionText" rows="3" placeholder="${isArabic ? 'أدخل نص السؤال' : 'Enter question text'}"></textarea>
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الخيارات:' : 'Options:'}</label>
                    <input type="text" id="option1" placeholder="${isArabic ? 'الخيار الأول' : 'First Option'}">
                    <input type="text" id="option2" placeholder="${isArabic ? 'الخيار الثاني' : 'Second Option'}">
                    <input type="text" id="option3" placeholder="${isArabic ? 'الخيار الثالث' : 'Third Option'}">
                    <input type="text" id="option4" placeholder="${isArabic ? 'الخيار الرابع' : 'Fourth Option'}">
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الإجابة الصحيحة:' : 'Correct Answer:'}</label>
                    <select id="correctAnswer">
                        <option value="1">${isArabic ? 'الخيار الأول' : 'First Option'}</option>
                        <option value="2">${isArabic ? 'الخيار الثاني' : 'Second Option'}</option>
                        <option value="3">${isArabic ? 'الخيار الثالث' : 'Third Option'}</option>
                        <option value="4">${isArabic ? 'الخيار الرابع' : 'Fourth Option'}</option>
                    </select>
                </div>
            `;
            break;
            
        case 'short':
            formHTML = `
                <div class="form-group">
                    <label>${isArabic ? 'نص السؤال:' : 'Question Text:'}</label>
                    <textarea id="questionText" rows="3" placeholder="${isArabic ? 'أدخل نص السؤال' : 'Enter question text'}"></textarea>
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الإجابة النموذجية:' : 'Model Answer:'}</label>
                    <textarea id="modelAnswer" rows="2" placeholder="${isArabic ? 'أدخل الإجابة النموذجية' : 'Enter model answer'}"></textarea>
                </div>
            `;
            break;
            
        case 'fillblank':
            formHTML = `
                <div class="form-group">
                    <label>${isArabic ? 'نص السؤال (استخدم _____ للفراغات):' : 'Question Text (use _____ for blanks):'}</label>
                    <textarea id="questionText" rows="3" placeholder="${isArabic ? 'مثال: العاصمة السودانية هي _____' : 'Example: The capital of Sudan is _____'}"></textarea>
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الإجابات (مفصولة بفاصلة):' : 'Answers (comma separated):'}</label>
                    <input type="text" id="answers" placeholder="${isArabic ? 'الخرطوم, الخرطوم' : 'Khartoum, Khartoum'}">
                </div>
            `;
            break;
            
        case 'diagram':
            formHTML = `
                <div class="form-group">
                    <label>${isArabic ? 'رفع الصورة/المخطط:' : 'Upload Image/Diagram:'}</label>
                    <input type="file" id="diagramImage" accept="image/*">
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'وصف السؤال:' : 'Question Description:'}</label>
                    <textarea id="questionText" rows="2" placeholder="${isArabic ? 'اكتب أسماء الأجزاء المشار إليها' : 'Name the indicated parts'}"></textarea>
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الإجابات المطلوبة:' : 'Required Answers:'}</label>
                    <textarea id="answers" rows="3" placeholder="${isArabic ? 'قائمة الأجزاء المطلوب تسميتها' : 'List of parts to be named'}"></textarea>
                </div>
            `;
            break;
            
        case 'flowchart':
            formHTML = `
                <div class="form-group">
                    <label>${isArabic ? 'رفع المخطط الانسيابي:' : 'Upload Flowchart:'}</label>
                    <input type="file" id="flowchartImage" accept="image/*">
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'نص السؤال:' : 'Question Text:'}</label>
                    <textarea id="questionText" rows="3" placeholder="${isArabic ? 'أدخل السؤال المتعلق بالمخطط' : 'Enter question related to the flowchart'}"></textarea>
                </div>
                <div class="form-group">
                    <label>${isArabic ? 'الإجابة النموذجية:' : 'Model Answer:'}</label>
                    <textarea id="modelAnswer" rows="3" placeholder="${isArabic ? 'أدخل الإجابة النموذجية' : 'Enter model answer'}"></textarea>
                </div>
            `;
            break;
    }
    
    formContainer.innerHTML = formHTML;
}

// Student Exam Variables
let currentExam = null;
let currentQuestionIndex = 0;
let studentAnswers = [];
let examTimer = null;
let timeRemaining = 0;
let examStartTime = null;
let isExamSubmitted = false;

// Start Exam Function
function startExam() {
    const studentId = document.getElementById('studentLoginId').value.trim();

    if (!studentId) {
        const message = currentLanguage === 'ar' ? 'يرجى إدخال رقم الطالب' : 'Please enter student ID';
        alert(message);
        return;
    }

    // Check if exam data exists (in real app, this would be from URL parameter)
    if (!examData || !examData.students || examData.students.length === 0) {
        const message = currentLanguage === 'ar' ? 'لا يوجد امتحان متاح حالياً' : 'No exam available currently';
        alert(message);
        return;
    }

    // Verify student is in the list
    const student = examData.students.find(s => s.id === studentId);
    if (!student) {
        const message = currentLanguage === 'ar' ? 'رقم الطالب غير مسجل في هذا الامتحان' : 'Student ID not registered for this exam';
        alert(message);
        return;
    }

    // Check if student already took the exam
    const existingResult = examData.results?.find(r => r.studentId === studentId);
    if (existingResult) {
        const message = currentLanguage === 'ar' ? 'لقد قمت بأداء هذا الامتحان مسبقاً' : 'You have already taken this exam';
        alert(message);
        return;
    }

    // Initialize exam
    currentExam = { ...examData };
    currentQuestionIndex = 0;
    studentAnswers = new Array(currentExam.questions.length).fill(null);
    timeRemaining = currentExam.duration * 60; // Convert minutes to seconds
    examStartTime = new Date();
    isExamSubmitted = false;

    // Shuffle questions and options if needed
    shuffleExamContent();

    // Hide login section and show exam interface
    document.getElementById('studentLoginSection').classList.add('hidden');
    document.getElementById('examInterface').classList.remove('hidden');

    // Update exam info
    updateExamInfo();

    // Start timer
    startExamTimer();

    // Display first question
    displayCurrentQuestion();

    // Prevent page refresh/close
    window.addEventListener('beforeunload', preventPageLeave);
}

// Shuffle Exam Content
function shuffleExamContent() {
    // Shuffle questions order
    currentExam.questions = shuffleArray(currentExam.questions);

    // Shuffle options for MCQ questions
    currentExam.questions.forEach(question => {
        if (question.type === 'mcq') {
            const correctAnswer = question.correctAnswer;
            const correctText = question.options[correctAnswer - 1];

            // Shuffle options
            question.options = shuffleArray(question.options);

            // Update correct answer index
            question.correctAnswer = question.options.indexOf(correctText) + 1;
        }
    });
}

// Shuffle Array Function (Enhanced)
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// Update Exam Info
function updateExamInfo() {
    document.getElementById('examInfoTitle').textContent = currentExam.examTitle;
    document.getElementById('examInfoDetails').textContent = `${currentExam.college} - ${currentExam.major}`;
}

// Start Exam Timer
function startExamTimer() {
    examTimer = setInterval(() => {
        if (timeRemaining <= 0) {
            // Time's up - auto submit
            autoSubmitExam();
            return;
        }

        timeRemaining--;
        updateTimerDisplay();

        // Change timer color based on remaining time
        updateTimerColor();

    }, 1000);
}

// Update Timer Display
function updateTimerDisplay() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('timeRemaining').textContent = timeString;
}

// Update Timer Color
function updateTimerColor() {
    const timer = document.getElementById('timer');
    const totalTime = currentExam.duration * 60;
    const percentage = (timeRemaining / totalTime) * 100;

    timer.classList.remove('warning', 'danger');

    if (percentage <= 10) {
        timer.classList.add('danger');
    } else if (percentage <= 25) {
        timer.classList.add('warning');
    }
}

// Display Current Question
function displayCurrentQuestion() {
    const question = currentExam.questions[currentQuestionIndex];
    const totalQuestions = currentExam.questions.length;

    // Update question number
    const questionNumberText = currentLanguage === 'ar' ?
        `السؤال ${currentQuestionIndex + 1} من ${totalQuestions}` :
        `Question ${currentQuestionIndex + 1} of ${totalQuestions}`;
    document.getElementById('questionNumber').textContent = questionNumberText;

    // Update progress bar
    const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;
    document.getElementById('progressFill').style.width = `${progress}%`;

    // Update question text
    document.getElementById('questionText').textContent = question.text;

    // Update options for MCQ questions
    if (question.type === 'mcq') {
        updateMCQOptions(question);
    }

    // Update navigation buttons
    updateNavigationButtons();
}

// Update MCQ Options
function updateMCQOptions(question) {
    const optionsContainer = document.getElementById('questionOptions');
    optionsContainer.innerHTML = '';

    const optionLabels = currentLanguage === 'ar' ? ['أ', 'ب', 'ج', 'د'] : ['A', 'B', 'C', 'D'];

    question.options.forEach((option, index) => {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'option-item';
        optionDiv.onclick = () => selectOption(index);

        const isSelected = studentAnswers[currentQuestionIndex] === index;
        if (isSelected) {
            optionDiv.classList.add('selected');
        }

        optionDiv.innerHTML = `
            <input type="radio" name="answer" value="${index}" id="option${index}" ${isSelected ? 'checked' : ''}>
            <span class="option-label">${optionLabels[index]}.</span>
            <span class="option-text">${option}</span>
        `;

        optionsContainer.appendChild(optionDiv);
    });
}

// Select Option
function selectOption(optionIndex) {
    if (isExamSubmitted) return;

    // Update student answer
    studentAnswers[currentQuestionIndex] = optionIndex;

    // Update UI
    document.querySelectorAll('.option-item').forEach((item, index) => {
        item.classList.toggle('selected', index === optionIndex);
        const radio = item.querySelector('input[type="radio"]');
        radio.checked = index === optionIndex;
    });

    // Auto-save answer (in real app, this would save to server)
    saveAnswerLocally();
}

// Save Answer Locally
function saveAnswerLocally() {
    const examProgress = {
        studentId: document.getElementById('studentLoginId').value,
        examId: currentExam.id,
        answers: studentAnswers,
        currentQuestion: currentQuestionIndex,
        timeRemaining: timeRemaining
    };
    localStorage.setItem('examProgress', JSON.stringify(examProgress));
}

// Update Navigation Buttons
function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const totalQuestions = currentExam.questions.length;

    // Previous button
    prevBtn.disabled = currentQuestionIndex === 0;

    // Next button
    nextBtn.disabled = currentQuestionIndex === totalQuestions - 1;

    // Update button text
    if (currentLanguage === 'ar') {
        document.getElementById('prevBtnText').textContent = 'السابق';
        document.getElementById('nextBtnText').textContent = currentQuestionIndex === totalQuestions - 1 ? 'انتهى' : 'التالي';
    } else {
        document.getElementById('prevBtnText').textContent = 'Previous';
        document.getElementById('nextBtnText').textContent = currentQuestionIndex === totalQuestions - 1 ? 'Finish' : 'Next';
    }
}

// Previous Question
function previousQuestion() {
    if (currentQuestionIndex > 0 && !isExamSubmitted) {
        currentQuestionIndex--;
        displayCurrentQuestion();
    }
}

// Next Question
function nextQuestion() {
    if (currentQuestionIndex < currentExam.questions.length - 1 && !isExamSubmitted) {
        currentQuestionIndex++;
        displayCurrentQuestion();
    }
}

// Submit Exam
function submitExam() {
    if (isExamSubmitted) return;

    const confirmMessage = currentLanguage === 'ar' ?
        'هل أنت متأكد من تسليم الامتحان؟ لن تتمكن من تعديل إجاباتك بعد التسليم.' :
        'Are you sure you want to submit the exam? You won\'t be able to modify your answers after submission.';

    if (!confirm(confirmMessage)) {
        return;
    }

    finishExam();
}

// Auto Submit Exam (when time expires)
function autoSubmitExam() {
    const message = currentLanguage === 'ar' ?
        'انتهى الوقت المحدد للامتحان. سيتم تسليم الامتحان تلقائياً.' :
        'Time is up! The exam will be submitted automatically.';

    alert(message);
    finishExam();
}

// Finish Exam
function finishExam() {
    isExamSubmitted = true;

    // Stop timer
    if (examTimer) {
        clearInterval(examTimer);
        examTimer = null;
    }

    // Disable all interactive elements
    disableExamInterface();

    // Calculate score
    const score = calculateScore();

    // Save result
    saveExamResult(score);

    // Show submission overlay
    showSubmissionModal(score);

    // Remove page leave prevention
    window.removeEventListener('beforeunload', preventPageLeave);

    // Clear local storage
    localStorage.removeItem('examProgress');
}

// Disable Exam Interface
function disableExamInterface() {
    const examInterface = document.getElementById('examInterface');
    examInterface.classList.add('exam-disabled');

    // Disable all buttons and inputs
    const buttons = examInterface.querySelectorAll('button');
    const inputs = examInterface.querySelectorAll('input');

    buttons.forEach(btn => btn.disabled = true);
    inputs.forEach(input => input.disabled = true);

    // Remove click handlers
    document.querySelectorAll('.option-item').forEach(item => {
        item.onclick = null;
        item.style.cursor = 'not-allowed';
    });
}

// Calculate Score
function calculateScore() {
    let correctAnswers = 0;
    const totalQuestions = currentExam.questions.length;

    currentExam.questions.forEach((question, index) => {
        if (question.type === 'mcq') {
            const studentAnswer = studentAnswers[index];
            const correctAnswer = question.correctAnswer - 1; // Convert to 0-based index

            if (studentAnswer === correctAnswer) {
                correctAnswers++;
            }
        }
    });

    return {
        correct: correctAnswers,
        total: totalQuestions,
        percentage: Math.round((correctAnswers / totalQuestions) * 100)
    };
}

// Save Exam Result
function saveExamResult(score) {
    const studentId = document.getElementById('studentLoginId').value;
    const endTime = new Date();
    const duration = Math.round((endTime - examStartTime) / 1000 / 60); // in minutes

    const result = {
        studentId: studentId,
        studentName: examData.students.find(s => s.id === studentId)?.name || 'Unknown',
        score: score.correct,
        totalQuestions: score.total,
        percentage: score.percentage,
        duration: duration,
        submissionTime: endTime.toLocaleString(),
        answers: [...studentAnswers]
    };

    // In real application, this would be sent to server
    if (!examData.results) {
        examData.results = [];
    }
    examData.results.push(result);

    // Update localStorage
    localStorage.setItem(`exam_${examData.id}`, JSON.stringify(examData));
}

// Show Submission Modal
function showSubmissionModal(score) {
    const overlay = document.getElementById('submissionOverlay');
    const title = document.getElementById('submissionTitle');
    const message = document.getElementById('submissionMessage');

    if (currentLanguage === 'ar') {
        title.textContent = 'تم تسليم الامتحان بنجاح';
        message.textContent = `شكراً لك، حصلت على ${score.correct} من ${score.total} (${score.percentage}%)`;
    } else {
        title.textContent = 'Exam Submitted Successfully';
        message.textContent = `Thank you, you scored ${score.correct} out of ${score.total} (${score.percentage}%)`;
    }

    overlay.classList.remove('hidden');
}

// Close Submission Modal
function closeSubmissionModal() {
    document.getElementById('submissionOverlay').classList.add('hidden');
}

// Prevent Page Leave
function preventPageLeave(e) {
    if (!isExamSubmitted) {
        e.preventDefault();
        e.returnValue = '';
        return '';
    }
}

// Keyboard Navigation
document.addEventListener('keydown', function(e) {
    if (!currentExam || isExamSubmitted) return;

    switch(e.key) {
        case 'ArrowLeft':
            if (currentLanguage === 'ar') {
                nextQuestion();
            } else {
                previousQuestion();
            }
            break;
        case 'ArrowRight':
            if (currentLanguage === 'ar') {
                previousQuestion();
            } else {
                nextQuestion();
            }
            break;
        case '1':
        case '2':
        case '3':
        case '4':
            const optionIndex = parseInt(e.key) - 1;
            if (optionIndex >= 0 && optionIndex < 4) {
                selectOption(optionIndex);
            }
            break;
        case 'Enter':
            if (e.ctrlKey) {
                submitExam();
            }
            break;
    }
});

// Initialize the application when page loads
document.addEventListener('DOMContentLoaded', initializeApp);
