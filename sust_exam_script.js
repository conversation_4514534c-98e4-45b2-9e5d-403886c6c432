// ===== JAVASCRIPT CODE FOR SUST ONLINE EXAM PLATFORM =====

// ===== DATA STRUCTURES AND GLOBAL VARIABLES =====

// SUST Colleges and Majors Data Structure - Complete Updated Version
const sustData = {
    "كلية الهندسة": ["مدرسة الهندسة المدنية", "مدرسة الهندسة الميكانيكية", "مدرسة هندسة الكهرباء - هندسة القدرة والآلات الكهربائية", "مدرسة هندسة الكهرباء - هندسة التحكم", "مدرسة هندسة المساحة", "مدرسة هندسة الإلكترونيات والاتصالات", "قسم الهندسة الطبية الحيوية", "قسم هندسة الطيران"],
    "كلية هندسة العمارة والتخطيط العمراني": ["هندسة العمارة", "التخطيط العمراني"],
    "كلية الهندسة الصناعية الكيميائية": ["الهندسة الكيميائية", "هندسة العمليات الصناعية", "الصناعات البتروكيماوية", "الصناعات الدوائية", "الصناعات الغذائية"],
    "كلية هندسة النفط": ["هندسة استكشاف النفط", "هندسة استخراج النفط", "هندسة إنتاج النفط والغاز"],
    "كلية هندسة المياه (ود المقبول)": ["هندسة الموارد المائية", "هندسة معالجة المياه", "هندسة الري والصرف"],
    "كلية الطب البشري": ["الطب العام", "الجراحة العامة", "الطب الباطني", "طب الأطفال", "النساء والتوليد", "طب المجتمع"],
    "كلية الصيدلة": ["الصيدلة الإكلينيكية", "الكيمياء الصيدلانية", "علم الأدوية", "الصيدلانيات"],
    "كلية طب الأسنان": ["طب الأسنان العام", "جراحة الفم والأسنان", "تقويم الأسنان", "طب أسنان الأطفال"],
    "كلية علوم الحاسوب وتقنية المعلومات": ["علوم الحاسوب", "تقنية المعلومات", "نظم المعلومات"],
    "كلية العلوم": ["الفيزياء", "الكيمياء", "الرياضيات", "علوم الأرض (الجيولوجيا)"],
    "كلية الدراسات التجارية": ["المحاسبة", "إدارة الأعمال", "التسويق", "التمويل والمصارف"],
    "كلية اللغات": ["اللغة الإنجليزية", "اللغة الفرنسية", "اللغة العربية"],
    "كلية الفنون الجميلة والتطبيقية": ["التصميم الصناعي", "التصميم الإيضاحي", "الطباعة والنشر", "الخزف", "النسيج"],
    "كلية التربية": ["التربية التقنية", "التربية الأسرية", "التربية الفنية"],
    "كلية الموسيقى والدراما": ["الموسيقى", "الدراما"],
    "كلية تكنولوجيا النفط والغاز": ["تكنولوجيا تشغيل المصافي", "تكنولوجيا إنتاج النفط والغاز"],
    "كلية علوم الاتصال": ["العلاقات العامة والإعلان", "الصحافة والنشر الإلكتروني", "الإذاعة والتلفزيون"],
    "كلية الزراعة": ["الإنتاج النباتي", "علوم وتكنولوجيا الأغذية", "الهندسة الزراعية"],
    "كلية الطب البيطري": ["الطب البيطري"],
    "كلية الغابات والمراعي": ["علوم الغابات", "علوم المراعي"]
};

// Global Application State
let examConfig = {
    college: '',
    major: '',
    courseName: '',
    examTitle: '',
    examDate: '',
    duration: 0,
    students: [],
    questions: [],
    shuffleAnswers: false
};

let currentStudent = null;
let currentQuestionIndex = 0;
let studentAnswers = [];
let examTimer = null;
let timeRemaining = 0;
let examResults = [];

// ===== INITIALIZATION =====

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCollegeDropdown();
    setupEventListeners();
});

// Initialize college dropdown with SUST data
function initializeCollegeDropdown() {
    const collegeSelect = document.getElementById('collegeSelect');
    
    // Clear existing options except the first one
    collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';
    
    // Populate with SUST colleges
    Object.keys(sustData).forEach(college => {
        const option = document.createElement('option');
        option.value = college;
        option.textContent = college;
        collegeSelect.appendChild(option);
    });
}

// Setup event listeners for dependent dropdowns
function setupEventListeners() {
    const collegeSelect = document.getElementById('collegeSelect');
    const majorSelect = document.getElementById('majorSelect');

    // College selection change handler - implements dependent dropdown logic
    collegeSelect.addEventListener('change', function() {
        const selectedCollege = this.value;
        
        // Clear and disable major dropdown
        majorSelect.innerHTML = '<option value="">اختر التخصص</option>';
        
        if (selectedCollege) {
            // Enable major dropdown
            majorSelect.disabled = false;
            
            // Populate majors for selected college
            const majors = sustData[selectedCollege];
            majors.forEach(major => {
                const option = document.createElement('option');
                option.value = major;
                option.textContent = major;
                majorSelect.appendChild(option);
            });
        } else {
            // Disable major dropdown if no college selected
            majorSelect.disabled = true;
        }
    });
}

// ===== STUDENT MANAGEMENT FUNCTIONS =====

// Add student manually
function addStudent() {
    const studentId = document.getElementById('studentIdInput').value.trim();
    const studentName = document.getElementById('studentNameInput').value.trim();
    
    if (!studentId || !studentName) {
        alert('يرجى إدخال رقم الطالب والاسم');
        return;
    }
    
    // Check for duplicate student ID
    if (examConfig.students.find(s => s.id === studentId)) {
        alert('رقم الطالب موجود مسبقاً');
        return;
    }
    
    // Add student to the list
    examConfig.students.push({
        id: studentId,
        name: studentName
    });
    
    // Clear input fields
    document.getElementById('studentIdInput').value = '';
    document.getElementById('studentNameInput').value = '';
    
    // Update student list display
    updateStudentList();
}

// Handle CSV file upload for bulk student addition
function handleCSVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        parseCSVData(csv);
    };
    reader.readAsText(file);
}

// Parse CSV data and add students
function parseCSVData(csvData) {
    const lines = csvData.split('\n');
    let addedCount = 0;
    let duplicateCount = 0;
    
    lines.forEach((line, index) => {
        // Skip empty lines
        if (!line.trim()) return;
        
        const [studentId, studentName] = line.split(',').map(item => item.trim());
        
        if (studentId && studentName) {
            // Check for duplicates
            if (!examConfig.students.find(s => s.id === studentId)) {
                examConfig.students.push({
                    id: studentId,
                    name: studentName
                });
                addedCount++;
            } else {
                duplicateCount++;
            }
        }
    });
    
    updateStudentList();
    alert(`تم إضافة ${addedCount} طالب. تم تجاهل ${duplicateCount} طالب مكرر.`);
}

// Update student list display
function updateStudentList() {
    const studentList = document.getElementById('studentList');
    
    if (examConfig.students.length === 0) {
        studentList.innerHTML = '<p style="text-align: center; color: #7f8c8d;">لم يتم إضافة طلاب بعد</p>';
        return;
    }
    
    let html = '';
    examConfig.students.forEach((student, index) => {
        html += `
            <div class="student-item">
                <span><strong>${student.id}</strong> - ${student.name}</span>
                <button type="button" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="removeStudent(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    });
    
    studentList.innerHTML = html;
}

// Remove student from list
function removeStudent(index) {
    if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
        examConfig.students.splice(index, 1);
        updateStudentList();
    }
}

// ===== QUESTION MANAGEMENT FUNCTIONS =====

// Add new question
function addQuestion() {
    const questionIndex = examConfig.questions.length;
    
    const questionObj = {
        id: questionIndex,
        text: '',
        options: ['', '', '', ''],
        correctAnswer: 0
    };
    
    examConfig.questions.push(questionObj);
    renderQuestionForm(questionIndex);
}

// Render question form
function renderQuestionForm(questionIndex) {
    const questionsList = document.getElementById('questionsList');
    
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-item';
    questionDiv.id = `question-${questionIndex}`;
    
    questionDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4>السؤال ${questionIndex + 1}</h4>
            <button type="button" class="btn btn-danger" onclick="removeQuestion(${questionIndex})" style="padding: 5px 10px;">
                <i class="fas fa-trash"></i> حذف
            </button>
        </div>
        
        <div class="form-group">
            <label>نص السؤال:</label>
            <textarea id="questionText-${questionIndex}" rows="3" placeholder="اكتب السؤال هنا..." onchange="updateQuestion(${questionIndex})"></textarea>
        </div>
        
        <div class="form-group">
            <label>الخيارات:</label>
            ${[0, 1, 2, 3].map(optionIndex => `
                <div class="answer-option">
                    <input type="radio" name="correct-${questionIndex}" value="${optionIndex}" 
                           ${optionIndex === 0 ? 'checked' : ''} 
                           onchange="updateCorrectAnswer(${questionIndex}, ${optionIndex})">
                    <input type="text" id="option-${questionIndex}-${optionIndex}" 
                           placeholder="الخيار ${['أ', 'ب', 'ج', 'د'][optionIndex]}" 
                           onchange="updateQuestion(${questionIndex})" style="flex: 1;">
                </div>
            `).join('')}
        </div>
    `;
    
    questionsList.appendChild(questionDiv);
}

// Update question data
function updateQuestion(questionIndex) {
    const question = examConfig.questions[questionIndex];
    
    // Update question text
    question.text = document.getElementById(`questionText-${questionIndex}`).value;
    
    // Update options
    for (let i = 0; i < 4; i++) {
        question.options[i] = document.getElementById(`option-${questionIndex}-${i}`).value;
    }
}

// Update correct answer
function updateCorrectAnswer(questionIndex, optionIndex) {
    examConfig.questions[questionIndex].correctAnswer = optionIndex;
}

// Remove question
function removeQuestion(questionIndex) {
    if (confirm('هل أنت متأكد من حذف هذا السؤال؟')) {
        examConfig.questions.splice(questionIndex, 1);
        
        // Re-render all questions with updated indices
        const questionsList = document.getElementById('questionsList');
        questionsList.innerHTML = '';
        
        examConfig.questions.forEach((_, index) => {
            renderQuestionForm(index);
            
            // Restore question data
            const question = examConfig.questions[index];
            document.getElementById(`questionText-${index}`).value = question.text;
            
            for (let i = 0; i < 4; i++) {
                document.getElementById(`option-${index}-${i}`).value = question.options[i];
            }
            
            document.querySelector(`input[name="correct-${index}"][value="${question.correctAnswer}"]`).checked = true;
        });
    }

// ===== EXAM CREATION AND MANAGEMENT =====

// Create exam and generate link
function createExam() {
    // Validate exam configuration
    if (!validateExamConfig()) {
        return;
    }

    // Save exam configuration
    examConfig.college = document.getElementById('collegeSelect').value;
    examConfig.major = document.getElementById('majorSelect').value;
    examConfig.courseName = document.getElementById('courseName').value;
    examConfig.examTitle = document.getElementById('examTitle').value;
    examConfig.examDate = document.getElementById('examDate').value;
    examConfig.duration = parseInt(document.getElementById('examDuration').value);
    examConfig.shuffleAnswers = document.getElementById('shuffleAnswers').checked;

    // Generate unique exam link (simulated)
    const examId = generateExamId();
    const examLink = `${window.location.origin}${window.location.pathname}?exam=${examId}`;

    // Display exam link
    document.getElementById('examLinkCode').textContent = examLink;
    document.getElementById('examLinkDisplay').style.display = 'block';

    // Hide setup view
    document.getElementById('teacherSetupView').classList.remove('active');

    alert('تم إنشاء الامتحان بنجاح!');
}

// Validate exam configuration
function validateExamConfig() {
    const college = document.getElementById('collegeSelect').value;
    const major = document.getElementById('majorSelect').value;
    const courseName = document.getElementById('courseName').value;
    const examTitle = document.getElementById('examTitle').value;
    const examDate = document.getElementById('examDate').value;
    const duration = document.getElementById('examDuration').value;

    if (!college || !major || !courseName || !examTitle || !examDate || !duration) {
        alert('يرجى ملء جميع معلومات الامتحان');
        return false;
    }

    if (examConfig.students.length === 0) {
        alert('يرجى إضافة طلاب للامتحان');
        return false;
    }

    if (examConfig.questions.length === 0) {
        alert('يرجى إضافة أسئلة للامتحان');
        return false;
    }

    // Validate questions
    for (let i = 0; i < examConfig.questions.length; i++) {
        const question = examConfig.questions[i];
        if (!question.text.trim()) {
            alert(`يرجى إدخال نص السؤال رقم ${i + 1}`);
            return false;
        }

        for (let j = 0; j < 4; j++) {
            if (!question.options[j].trim()) {
                alert(`يرجى إدخال جميع خيارات السؤال رقم ${i + 1}`);
                return false;
            }
        }
    }

    return true;
}

// Generate unique exam ID
function generateExamId() {
    return 'SUST-' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// ===== FISHER-YATES SHUFFLE ALGORITHM =====

// Shuffle array using Fisher-Yates algorithm
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// Shuffle question options for a student
function shuffleQuestionOptions(questions) {
    return questions.map(question => {
        if (!examConfig.shuffleAnswers) {
            return {
                ...question,
                shuffledOptions: question.options,
                shuffledCorrectAnswer: question.correctAnswer
            };
        }

        // Create array of option indices
        const indices = [0, 1, 2, 3];
        const shuffledIndices = shuffleArray(indices);

        // Create shuffled options
        const shuffledOptions = shuffledIndices.map(index => question.options[index]);

        // Find new position of correct answer
        const shuffledCorrectAnswer = shuffledIndices.indexOf(question.correctAnswer);

        return {
            ...question,
            shuffledOptions,
            shuffledCorrectAnswer
        };
    });
}

// ===== STUDENT EXAM INTERFACE =====

// Show student exam view
function showStudentView() {
    document.getElementById('examLinkDisplay').style.display = 'none';
    document.getElementById('studentExamView').classList.add('active');
    document.getElementById('studentLogin').style.display = 'block';
    document.getElementById('examInterface').style.display = 'none';
}

// Start exam for student
function startExam() {
    const studentId = document.getElementById('studentIdLogin').value.trim();

    if (!studentId) {
        alert('يرجى إدخال رقم الطالب');
        return;
    }

    // Verify student is in the list
    const student = examConfig.students.find(s => s.id === studentId);
    if (!student) {
        alert('رقم الطالب غير موجود في قائمة الطلاب المسجلين');
        return;
    }

    // Check if student already took the exam
    if (examResults.find(r => r.studentId === studentId)) {
        alert('لقد قمت بأداء هذا الامتحان مسبقاً');
        return;
    }

    // Set current student
    currentStudent = student;
    currentQuestionIndex = 0;
    studentAnswers = new Array(examConfig.questions.length).fill(null);

    // Shuffle questions for this student
    const shuffledQuestions = shuffleQuestionOptions(examConfig.questions);
    currentStudent.questions = shuffledQuestions;

    // Setup exam interface
    document.getElementById('studentLogin').style.display = 'none';
    document.getElementById('examInterface').style.display = 'block';

    // Set exam header
    document.getElementById('examHeader').textContent =
        `${examConfig.examTitle} - ${examConfig.courseName}`;

    // Start timer
    startExamTimer();

    // Display first question
    displayQuestion();
}

// ===== COUNTDOWN TIMER IMPLEMENTATION =====

// Start exam countdown timer
function startExamTimer() {
    timeRemaining = examConfig.duration * 60; // Convert minutes to seconds

    examTimer = setInterval(() => {
        timeRemaining--;
        updateTimerDisplay();

        // Warning when 5 minutes remaining
        if (timeRemaining <= 300 && timeRemaining > 0) {
            document.getElementById('examTimer').classList.add('warning');
        }

        // Auto-submit when time expires
        if (timeRemaining <= 0) {
            clearInterval(examTimer);
            autoSubmitExam();
        }
    }, 1000);

    updateTimerDisplay();
}

// Update timer display
function updateTimerDisplay() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('timerDisplay').textContent = display;
}

// Auto-submit exam when timer expires
function autoSubmitExam() {
    alert('انتهى وقت الامتحان. سيتم تسليم الامتحان تلقائياً.');

    // Disable all interactive elements
    disableExamInterface();

    // Submit exam
    processExamSubmission();
}

// Disable exam interface (critical security feature)
function disableExamInterface() {
    // Disable all radio buttons
    const radioButtons = document.querySelectorAll('#currentQuestion input[type="radio"]');
    radioButtons.forEach(radio => radio.disabled = true);

    // Disable navigation buttons
    document.getElementById('prevBtn').disabled = true;
    document.getElementById('nextBtn').disabled = true;

    // Disable submit button
    const submitBtn = document.querySelector('button[onclick="submitExam()"]');
    if (submitBtn) submitBtn.disabled = true;

    // Add visual indication
    document.getElementById('examInterface').classList.add('disabled');
}

// ===== QUESTION DISPLAY AND NAVIGATION =====

// Display current question
function displayQuestion() {
    const question = currentStudent.questions[currentQuestionIndex];
    const questionDiv = document.getElementById('currentQuestion');

    let html = `
        <h3>السؤال ${currentQuestionIndex + 1} من ${examConfig.questions.length}</h3>
        <p style="font-size: 1.1rem; margin: 20px 0; line-height: 1.6;">${question.text}</p>
        <div style="margin: 20px 0;">
    `;

    // Display options (shuffled if enabled)
    const options = question.shuffledOptions || question.options;
    const optionLabels = ['أ', 'ب', 'ج', 'د'];

    options.forEach((option, index) => {
        const isChecked = studentAnswers[currentQuestionIndex] === index ? 'checked' : '';
        html += `
            <div class="answer-option">
                <input type="radio" name="currentAnswer" value="${index}" ${isChecked}
                       onchange="saveAnswer(${index})">
                <label style="cursor: pointer; flex: 1;">${optionLabels[index]}. ${option}</label>
            </div>
        `;
    });

    html += '</div>';
    questionDiv.innerHTML = html;

    // Update question counter
    document.getElementById('questionCounter').textContent =
        `السؤال ${currentQuestionIndex + 1} من ${examConfig.questions.length}`;

    // Update navigation buttons
    document.getElementById('prevBtn').style.display = currentQuestionIndex > 0 ? 'inline-block' : 'none';
    document.getElementById('nextBtn').textContent =
        currentQuestionIndex < examConfig.questions.length - 1 ? 'التالي' : 'الانتهاء';
}

// Save student answer
function saveAnswer(answerIndex) {
    studentAnswers[currentQuestionIndex] = answerIndex;
}

// Navigate to previous question
function previousQuestion() {
    if (currentQuestionIndex > 0) {
        currentQuestionIndex--;
        displayQuestion();
    }
}

// Navigate to next question
function nextQuestion() {
    if (currentQuestionIndex < examConfig.questions.length - 1) {
        currentQuestionIndex++;
        displayQuestion();
    } else {
        // Last question - show submit confirmation
        if (confirm('هل أنت متأكد من تسليم الامتحان؟ لن تتمكن من التعديل بعد التسليم.')) {
            submitExam();
        }
    }
}

// Submit exam manually
function submitExam() {
    if (confirm('هل أنت متأكد من تسليم الامتحان؟')) {
        clearInterval(examTimer);
        disableExamInterface();
        processExamSubmission();
    }
}

// ===== AUTO-GRADING SYSTEM =====

// Process exam submission and calculate grade
function processExamSubmission() {
    const submissionTime = new Date().toLocaleString('ar-SA');
    let correctAnswers = 0;

    // Calculate score by comparing student answers with correct answers
    for (let i = 0; i < examConfig.questions.length; i++) {
        const question = currentStudent.questions[i];
        const studentAnswer = studentAnswers[i];

        // Get the correct answer index for shuffled options
        const correctAnswerIndex = question.shuffledCorrectAnswer !== undefined
            ? question.shuffledCorrectAnswer
            : question.correctAnswer;

        if (studentAnswer === correctAnswerIndex) {
            correctAnswers++;
        }
    }

    // Calculate percentage
    const percentage = Math.round((correctAnswers / examConfig.questions.length) * 100);

    // Create result object
    const result = {
        studentId: currentStudent.id,
        studentName: currentStudent.name,
        score: correctAnswers,
        totalQuestions: examConfig.questions.length,
        percentage: percentage,
        submissionTime: submissionTime,
        answers: [...studentAnswers]
    };

    // Add to results array
    examResults.push(result);

    // Show completion message
    alert(`تم تسليم الامتحان بنجاح!\nدرجتك: ${correctAnswers}/${examConfig.questions.length} (${percentage}%)`);

    // Reset student exam view
    resetStudentExamView();
}

// Reset student exam view for next student
function resetStudentExamView() {
    document.getElementById('studentExamView').classList.remove('active');
    document.getElementById('studentLogin').style.display = 'block';
    document.getElementById('examInterface').style.display = 'none';
    document.getElementById('examInterface').classList.remove('disabled');
    document.getElementById('studentIdLogin').value = '';
    document.getElementById('examTimer').classList.remove('warning');

    // Re-enable interface elements
    const radioButtons = document.querySelectorAll('#currentQuestion input[type="radio"]');
    radioButtons.forEach(radio => radio.disabled = false);
    document.getElementById('prevBtn').disabled = false;
    document.getElementById('nextBtn').disabled = false;

    currentStudent = null;
    currentQuestionIndex = 0;
    studentAnswers = [];
}

// ===== RESULTS VIEW AND STATISTICS =====

// Show teacher results view
function showResultsView() {
    document.getElementById('examLinkDisplay').style.display = 'none';
    document.getElementById('teacherResultsView').classList.add('active');
    updateResultsDisplay();
}

// Update results display
function updateResultsDisplay() {
    updateStatistics();
    updateResultsTable();
}

// Update statistics
function updateStatistics() {
    const totalStudents = examResults.length;
    let totalPercentage = 0;

    examResults.forEach(result => {
        totalPercentage += result.percentage;
    });

    const averageScore = totalStudents > 0 ? Math.round(totalPercentage / totalStudents) : 0;

    document.getElementById('totalStudents').textContent = totalStudents;
    document.getElementById('averageScore').textContent = averageScore + '%';
}

// Update results table
function updateResultsTable() {
    const tbody = document.getElementById('resultsTableBody');

    if (examResults.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #7f8c8d;">لا توجد نتائج بعد</td></tr>';
        return;
    }

    let html = '';
    examResults.forEach(result => {
        const scoreClass = getScoreClass(result.percentage);
        html += `
            <tr>
                <td>${result.studentId}</td>
                <td>${result.studentName}</td>
                <td>${result.score}/${result.totalQuestions}</td>
                <td><span class="score-badge ${scoreClass}">${result.percentage}%</span></td>
                <td>${result.submissionTime}</td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// Get score class for styling
function getScoreClass(percentage) {
    if (percentage >= 85) return 'score-excellent';
    if (percentage >= 70) return 'score-good';
    return 'score-poor';
}

// ===== VIEW MANAGEMENT =====

// Show teacher setup view
function showTeacherSetup() {
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
        view.classList.remove('active');
    });

    // Hide exam link display
    document.getElementById('examLinkDisplay').style.display = 'none';

    // Show teacher setup
    document.getElementById('teacherSetupView').classList.add('active');

    // Reset exam configuration for new exam
    resetExamConfiguration();
}

// Reset exam configuration
function resetExamConfiguration() {
    // Clear form fields
    document.getElementById('collegeSelect').value = '';
    document.getElementById('majorSelect').value = '';
    document.getElementById('majorSelect').disabled = true;
    document.getElementById('courseName').value = '';
    document.getElementById('examTitle').value = '';
    document.getElementById('examDate').value = '';
    document.getElementById('examDuration').value = '';
    document.getElementById('shuffleAnswers').checked = false;
    document.getElementById('studentIdInput').value = '';
    document.getElementById('studentNameInput').value = '';

    // Reset data
    examConfig = {
        college: '',
        major: '',
        courseName: '',
        examTitle: '',
        examDate: '',
        duration: 0,
        students: [],
        questions: [],
        shuffleAnswers: false
    };

    examResults = [];

    // Clear displays
    updateStudentList();
    document.getElementById('questionsList').innerHTML = '';
}

// ===== KEYBOARD SHORTCUTS =====

// Add keyboard shortcuts for better user experience
document.addEventListener('keydown', function(event) {
    // Only apply shortcuts in exam interface
    if (document.getElementById('examInterface').style.display !== 'block') return;

    switch(event.key) {
        case 'ArrowLeft':
            event.preventDefault();
            nextQuestion();
            break;
        case 'ArrowRight':
            event.preventDefault();
            previousQuestion();
            break;
        case '1':
        case '2':
        case '3':
        case '4':
            event.preventDefault();
            const answerIndex = parseInt(event.key) - 1;
            const radioButton = document.querySelector(`input[name="currentAnswer"][value="${answerIndex}"]`);
            if (radioButton && !radioButton.disabled) {
                radioButton.checked = true;
                saveAnswer(answerIndex);
            }
            break;
    }
});

// ===== INITIALIZATION COMPLETE =====

console.log('SUST Online Exam Platform initialized successfully');
console.log('Developed for Sudan University of Science and Technology');
}
