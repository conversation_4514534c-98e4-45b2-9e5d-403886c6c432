<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Online Exam Platform</title>
    <link rel="stylesheet" href="css/main.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .redirect-container {
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
        }
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .redirect-message {
            color: #666;
            margin-top: 1rem;
        }
    </style>
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fonlineexa5436back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body>
    <div class="redirect-container">
        <div class="logo">📚 Online Exam Platform</div>
        <div class="loading-spinner"></div>
        <div class="redirect-message">Redirecting to Student Login...</div>
    </div>

    <script>
        // Redirect to student exam login page after 2 seconds
        setTimeout(function() {
            window.location.href = 'pages/student_exam_login.html';
        }, 2000);
    </script>
<script id="dhws-dataInjector" src="/public/dhws-data-injector.js"></script>
</body>
</html>