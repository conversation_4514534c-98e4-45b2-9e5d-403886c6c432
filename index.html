<!DOCTYPE html>
<html lang="en" dir="ltr" id="htmlRoot">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Online Exam Platform - Dr. <PERSON></title>
    <link rel="stylesheet" href="css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        /* Custom styles for the application */
        .view {
            display: none;
        }
        .view.active {
            display: block;
        }

        /* RTL Support */
        [dir="rtl"] {
            font-family: 'Cairo', 'Inter', sans-serif;
        }
        [dir="ltr"] {
            font-family: 'Inter', 'Cairo', sans-serif;
        }

        /* Loading animation */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Question type specific styles */
        .question-container {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: border-color 0.3s;
        }

        .question-container:hover {
            border-color: #3b82f6;
        }

        .figure-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .figure-upload-area:hover {
            border-color: #3b82f6;
        }

        .flowchart-canvas {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
        }

        /* Timer styles */
        .timer {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .timer.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Results chart container */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        /* Author info footer */
        .author-footer {
            background: linear-gradient(135deg, #1e40af, #3730a3);
            color: white;
            padding: 1rem;
            text-align: center;
            font-size: 0.875rem;
        }

        /* Navigation active state */
        .nav-item.active {
            background: #3b82f6;
            color: white;
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-gray-900" data-translate="app_title">ExamPro Platform</h1>
                        <p class="text-sm text-gray-500" data-translate="app_subtitle">Online Examination System</p>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-1">
                    <button onclick="showView('teacher-setup')" class="nav-item px-4 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:text-blue-600 hover:bg-blue-50" data-translate="nav_teacher_setup">
                        <i class="fas fa-plus-circle mr-2"></i>Teacher Setup
                    </button>
                    <button onclick="showView('student-registration')" class="nav-item px-4 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:text-blue-600 hover:bg-blue-50" data-translate="nav_student_registration">
                        <i class="fas fa-user-plus mr-2"></i>Student Registration
                    </button>
                    <button onclick="showView('student-exam')" class="nav-item px-4 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:text-blue-600 hover:bg-blue-50" data-translate="nav_student_exam">
                        <i class="fas fa-user-graduate mr-2"></i>Student Exam
                    </button>
                    <button onclick="showView('teacher-results')" class="nav-item px-4 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:text-blue-600 hover:bg-blue-50" data-translate="nav_teacher_results">
                        <i class="fas fa-chart-bar mr-2"></i>Results
                    </button>
                </nav>

                <!-- Language Toggle and Mobile Menu -->
                <div class="flex items-center space-x-4">
                    <button onclick="toggleLanguage()" class="text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-lg hover:bg-blue-50">
                        <i class="fas fa-globe mr-1"></i>
                        <span id="currentLang">EN</span>
                    </button>

                    <!-- Mobile menu button -->
                    <button onclick="toggleMobileMenu()" class="md:hidden text-gray-600 hover:text-blue-600">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobileMenu" class="md:hidden hidden border-t border-gray-200 py-2">
                <button onclick="showView('teacher-setup')" class="block w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-blue-50" data-translate="nav_teacher_setup">
                    <i class="fas fa-plus-circle mr-2"></i>Teacher Setup
                </button>
                <button onclick="showView('student-registration')" class="block w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-blue-50" data-translate="nav_student_registration">
                    <i class="fas fa-user-plus mr-2"></i>Student Registration
                </button>
                <button onclick="showView('student-exam')" class="block w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-blue-50" data-translate="nav_student_exam">
                    <i class="fas fa-user-graduate mr-2"></i>Student Exam
                </button>
                <button onclick="showView('teacher-results')" class="block w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-blue-50" data-translate="nav_teacher_results">
                    <i class="fas fa-chart-bar mr-2"></i>Results
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="flex-1">
        <!-- Teacher Setup View -->
        <div id="teacher-setup" class="view">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2" data-translate="teacher_setup_title">Create New Exam</h2>
                    <p class="text-gray-600" data-translate="teacher_setup_subtitle">Design comprehensive exams with multiple question types</p>
                </div>

                <!-- Exam Basic Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="exam_basic_info">Exam Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_title">Exam Title</label>
                            <input type="text" id="examTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter exam title">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_duration">Duration (minutes)</label>
                            <input type="number" id="examDuration" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="60" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_subject">Subject</label>
                            <input type="text" id="examSubject" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter subject">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_total_marks">Total Marks</label>
                            <input type="number" id="examTotalMarks" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="100" min="1">
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_instructions">Instructions</label>
                        <textarea id="examInstructions" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter exam instructions for students"></textarea>
                    </div>
                </div>

                <!-- Question Types Panel -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="add_questions">Add Questions</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                        <button onclick="addQuestion('mcq')" class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fas fa-list-ul text-2xl text-blue-600 mb-2"></i>
                            <span class="text-sm font-medium" data-translate="mcq">MCQ</span>
                        </button>
                        <button onclick="addQuestion('short')" class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fas fa-edit text-2xl text-green-600 mb-2"></i>
                            <span class="text-sm font-medium" data-translate="short_answer">Short Answer</span>
                        </button>
                        <button onclick="addQuestion('fillblank')" class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fas fa-underline text-2xl text-purple-600 mb-2"></i>
                            <span class="text-sm font-medium" data-translate="fill_blank">Fill Blanks</span>
                        </button>
                        <button onclick="addQuestion('figure')" class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fas fa-image text-2xl text-orange-600 mb-2"></i>
                            <span class="text-sm font-medium" data-translate="figure_naming">Figure Naming</span>
                        </button>
                        <button onclick="addQuestion('flowchart')" class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fas fa-project-diagram text-2xl text-red-600 mb-2"></i>
                            <span class="text-sm font-medium" data-translate="flowchart">Flowchart</span>
                        </button>
                    </div>
                </div>

                <!-- Questions Container -->
                <div id="questionsContainer" class="space-y-6 mb-6">
                    <!-- Questions will be dynamically added here -->
                </div>

                <!-- Action Buttons -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex flex-wrap gap-4">
                        <button onclick="previewExam()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-eye mr-2"></i>
                            <span data-translate="preview_exam">Preview Exam</span>
                        </button>
                        <button onclick="saveExam()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            <span data-translate="save_exam">Save Exam</span>
                        </button>
                        <button onclick="generateGoogleForm()" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fab fa-google mr-2"></i>
                            <span data-translate="google_forms">Google Forms</span>
                        </button>
                        <button onclick="generateShareLink()" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            <i class="fas fa-share-alt mr-2"></i>
                            <span data-translate="share_link">Share Link</span>
                        </button>
                        <button onclick="uploadQuestions()" class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-upload mr-2"></i>
                            <span data-translate="upload_questions">Upload Questions</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Registration View -->
        <div id="student-registration" class="view">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2" data-translate="student_registration_title">Student Registration</h2>
                    <p class="text-gray-600" data-translate="student_registration_subtitle">Manage student enrollment and registration data</p>
                </div>

                <!-- Registration Methods -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Manual Registration -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="manual_registration">Manual Registration</h3>
                        <form id="studentRegistrationForm" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="student_id">Student ID</label>
                                    <input type="text" id="regStudentId" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter student ID">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="full_name">Full Name</label>
                                    <input type="text" id="regStudentName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter full name">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="email">Email</label>
                                    <input type="email" id="regStudentEmail" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter email address">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="phone">Phone</label>
                                    <input type="tel" id="regStudentPhone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter phone number">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="department">Department</label>
                                    <select id="regStudentDepartment" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select Department</option>
                                        <option value="BME">Biomedical Engineering</option>
                                        <option value="CS">Computer Science</option>
                                        <option value="EE">Electrical Engineering</option>
                                        <option value="ME">Mechanical Engineering</option>
                                        <option value="CE">Civil Engineering</option>
                                        <option value="CHE">Chemical Engineering</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="year_level">Year Level</label>
                                    <select id="regStudentYear" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select Year</option>
                                        <option value="1">First Year</option>
                                        <option value="2">Second Year</option>
                                        <option value="3">Third Year</option>
                                        <option value="4">Fourth Year</option>
                                        <option value="5">Fifth Year</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex space-x-4">
                                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    <span data-translate="add_student">Add Student</span>
                                </button>
                                <button type="button" onclick="clearRegistrationForm()" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-eraser mr-2"></i>
                                    <span data-translate="clear_form">Clear Form</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- File Upload Registration -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="file_upload_registration">File Upload Registration</h3>
                        <div class="space-y-4">
                            <!-- Upload Area -->
                            <div class="upload-area" id="studentFileUploadArea" onclick="document.getElementById('studentFileUpload').click()">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-lg font-medium text-gray-700 mb-2" data-translate="upload_student_file">Upload Student List</p>
                                <p class="text-sm text-gray-500 mb-4" data-translate="supported_formats">Supported formats: CSV, Excel, Word, PDF</p>
                                <div class="flex justify-center space-x-4 text-xs text-gray-400">
                                    <span><i class="fas fa-file-csv mr-1"></i>CSV</span>
                                    <span><i class="fas fa-file-excel mr-1"></i>Excel</span>
                                    <span><i class="fas fa-file-word mr-1"></i>Word</span>
                                    <span><i class="fas fa-file-pdf mr-1"></i>PDF</span>
                                </div>
                            </div>

                            <!-- File Format Instructions -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="text-sm font-semibold text-blue-900 mb-2" data-translate="file_format_guide">File Format Guide</h4>
                                <div class="text-sm text-blue-800 space-y-1">
                                    <p><strong>CSV Format:</strong> ID, Name, Email, Phone, Department, Year</p>
                                    <p><strong>Excel:</strong> Same columns as CSV in first sheet</p>
                                    <p><strong>Word:</strong> Table with same column structure</p>
                                    <p><strong>PDF:</strong> Text will be extracted and parsed</p>
                                </div>
                            </div>

                            <!-- Sample Download -->
                            <div class="flex space-x-4">
                                <button type="button" onclick="downloadSampleCSV()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                    <i class="fas fa-download mr-2"></i>
                                    <span data-translate="download_sample">Download Sample CSV</span>
                                </button>
                                <button type="button" onclick="downloadTemplate()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                                    <i class="fas fa-file-download mr-2"></i>
                                    <span data-translate="download_template">Download Template</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Student List Management -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900" data-translate="registered_students">Registered Students</h3>
                        <div class="flex space-x-2">
                            <button type="button" onclick="exportStudents('csv')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                <i class="fas fa-file-csv mr-2"></i>
                                <span data-translate="export_csv">Export CSV</span>
                            </button>
                            <button type="button" onclick="exportStudents('excel')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                <i class="fas fa-file-excel mr-2"></i>
                                <span data-translate="export_excel">Export Excel</span>
                            </button>
                            <button type="button" onclick="clearAllStudents()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm">
                                <i class="fas fa-trash mr-2"></i>
                                <span data-translate="clear_all">Clear All</span>
                            </button>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <input type="text" id="studentSearch" placeholder="Search students..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onkeyup="filterStudents()">
                        </div>
                        <div>
                            <select id="departmentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onchange="filterStudents()">
                                <option value="">All Departments</option>
                                <option value="BME">Biomedical Engineering</option>
                                <option value="CS">Computer Science</option>
                                <option value="EE">Electrical Engineering</option>
                                <option value="ME">Mechanical Engineering</option>
                                <option value="CE">Civil Engineering</option>
                                <option value="CHE">Chemical Engineering</option>
                            </select>
                        </div>
                        <div>
                            <select id="yearFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onchange="filterStudents()">
                                <option value="">All Years</option>
                                <option value="1">First Year</option>
                                <option value="2">Second Year</option>
                                <option value="3">Third Year</option>
                                <option value="4">Fourth Year</option>
                                <option value="5">Fifth Year</option>
                            </select>
                        </div>
                    </div>

                    <!-- Students Table -->
                    <div class="overflow-x-auto">
                        <table id="studentsTable" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="student_id">Student ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="name">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="email">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="phone">Phone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="department">Department</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="year">Year</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="studentsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Students will be dynamically populated -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="flex items-center justify-between mt-6">
                        <div class="text-sm text-gray-700">
                            <span data-translate="showing">Showing</span> <span id="studentsShowing">0</span> <span data-translate="of">of</span> <span id="studentsTotal">0</span> <span data-translate="students">students</span>
                        </div>
                        <div class="flex space-x-2">
                            <button type="button" onclick="previousStudentsPage()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                <i class="fas fa-chevron-left mr-1"></i>
                                <span data-translate="previous">Previous</span>
                            </button>
                            <span id="studentsPageInfo" class="px-3 py-1 text-sm text-gray-700">Page 1 of 1</span>
                            <button type="button" onclick="nextStudentsPage()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                <span data-translate="next">Next</span>
                                <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Exam View -->
        <div id="student-exam" class="view">
            <!-- Student Login Section -->
            <div id="studentLogin" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
                <div class="max-w-md w-full mx-4">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <div class="text-center mb-8">
                            <div class="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-graduate text-white text-2xl"></i>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900" data-translate="student_login">Student Login</h2>
                            <p class="text-gray-600 mt-2" data-translate="enter_credentials">Enter your credentials to access the exam</p>
                        </div>

                        <form id="studentLoginForm" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="student_id">Student ID</label>
                                <input type="text" id="studentId" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your student ID">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="student_name">Full Name</label>
                                <input type="text" id="studentName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your full name">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam_code">Exam Code</label>
                                <input type="text" id="examCode" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter exam code">
                            </div>
                            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                <span data-translate="start_exam">Start Exam</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Exam Interface -->
            <div id="examInterface" class="hidden">
                <!-- Exam Header -->
                <div class="bg-white shadow-sm border-b border-gray-200 sticky top-16 z-40">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 id="examTitleDisplay" class="text-lg font-semibold text-gray-900"></h3>
                                <p class="text-sm text-gray-600">
                                    <span data-translate="student">Student:</span> <span id="studentNameDisplay"></span> |
                                    <span data-translate="id">ID:</span> <span id="studentIdDisplay"></span>
                                </p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div id="examTimer" class="timer">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span id="timeRemaining">00:00:00</span>
                                </div>
                                <button type="button" onclick="submitExam()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    <span data-translate="submit_exam">Submit Exam</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Navigation -->
                <div class="bg-gray-50 border-b border-gray-200">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <span class="text-sm font-medium text-gray-700" data-translate="question">Question:</span>
                                <div id="questionNavigation" class="flex space-x-2">
                                    <!-- Question numbers will be dynamically added -->
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button type="button" onclick="previousQuestion()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                    <i class="fas fa-chevron-left mr-1"></i>
                                    <span data-translate="previous">Previous</span>
                                </button>
                                <button type="button" onclick="nextQuestion()" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                    <span data-translate="next">Next</span>
                                    <i class="fas fa-chevron-right ml-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Content -->
                <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div id="currentQuestion" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <!-- Current question will be displayed here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Results View -->
        <div id="teacher-results" class="view">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2" data-translate="results_dashboard">Results Dashboard</h2>
                    <p class="text-gray-600" data-translate="results_subtitle">View and analyze exam performance</p>
                </div>

                <!-- Exam Selection -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="select_exam">Select Exam</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="exam">Exam</label>
                            <select id="examSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select an exam...</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="date_range">Date Range</label>
                            <input type="date" id="dateFrom" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button type="button" onclick="loadResults()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-search mr-2"></i>
                                <span data-translate="load_results">Load Results</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Overview -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-blue-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600" data-translate="total_students">Total Students</p>
                                <p id="totalStudents" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-green-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600" data-translate="average_score">Average Score</p>
                                <p id="averageScore" class="text-2xl font-bold text-gray-900">0%</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-trophy text-yellow-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600" data-translate="highest_score">Highest Score</p>
                                <p id="highestScore" class="text-2xl font-bold text-gray-900">0%</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600" data-translate="pass_rate">Pass Rate</p>
                                <p id="passRate" class="text-2xl font-bold text-gray-900">0%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Analysis -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="score_distribution">Score Distribution</h3>
                        <div class="chart-container">
                            <canvas id="scoreChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4" data-translate="question_analysis">Question Analysis</h3>
                        <div class="chart-container">
                            <canvas id="questionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Student Results Table -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900" data-translate="student_results">Student Results</h3>
                        <div class="flex space-x-2">
                            <button type="button" onclick="exportResults('csv')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                <i class="fas fa-file-csv mr-2"></i>
                                <span data-translate="export_csv">Export CSV</span>
                            </button>
                            <button type="button" onclick="exportResults('pdf')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm">
                                <i class="fas fa-file-pdf mr-2"></i>
                                <span data-translate="export_pdf">Export PDF</span>
                            </button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table id="resultsTable" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="student_id">Student ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="name">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="score">Score</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="percentage">Percentage</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="time_taken">Time Taken</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="status">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-translate="actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Results will be dynamically populated -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Author Footer -->
    <footer class="author-footer">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="font-semibold">Dr. Mohammed Yagoub Esmail</p>
                <p>SUST - BME (Biomedical Engineering) | © 2025</p>
                <p>
                    <i class="fas fa-envelope mr-2"></i><EMAIL> |
                    <i class="fas fa-phone mr-2"></i>+249912867327 | +966538076790
                </p>
                <p class="text-xs mt-2 opacity-75">All Rights Reserved - Online Exam Platform</p>
            </div>
        </div>
    </footer>

    <!-- Hidden file input for uploads -->
    <input type="file" id="fileUpload" accept=".json,.csv,.txt" style="display: none;" onchange="handleFileUpload(event)">
    <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
    <input type="file" id="studentFileUpload" accept=".csv,.xlsx,.xls,.doc,.docx,.pdf,.txt" style="display: none;" onchange="handleStudentFileUpload(event)">

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Main JavaScript -->
    <script>
        // Global variables
        let currentLanguage = 'en';
        let currentView = 'teacher-setup';
        let currentExam = null;
        let currentQuestionIndex = 0;
        let examTimer = null;
        let examStartTime = null;
        let studentAnswers = {};
        let questions = [];
        let questionCounter = 0;
        let registeredStudents = [];
        let currentStudentsPage = 1;
        let studentsPerPage = 10;
        let filteredStudents = [];

        // Language translations
        const translations = {
            en: {
                app_title: "ExamPro Platform",
                app_subtitle: "Online Examination System",
                nav_teacher_setup: "Teacher Setup",
                nav_student_registration: "Student Registration",
                nav_student_exam: "Student Exam",
                nav_teacher_results: "Results",
                teacher_setup_title: "Create New Exam",
                teacher_setup_subtitle: "Design comprehensive exams with multiple question types",
                exam_basic_info: "Exam Information",
                exam_title: "Exam Title",
                exam_duration: "Duration (minutes)",
                exam_subject: "Subject",
                exam_total_marks: "Total Marks",
                exam_instructions: "Instructions",
                add_questions: "Add Questions",
                mcq: "MCQ",
                short_answer: "Short Answer",
                fill_blank: "Fill Blanks",
                figure_naming: "Figure Naming",
                flowchart: "Flowchart",
                preview_exam: "Preview Exam",
                save_exam: "Save Exam",
                google_forms: "Google Forms",
                share_link: "Share Link",
                upload_questions: "Upload Questions",
                student_login: "Student Login",
                enter_credentials: "Enter your credentials to access the exam",
                student_id: "Student ID",
                student_name: "Full Name",
                exam_code: "Exam Code",
                start_exam: "Start Exam",
                student: "Student",
                id: "ID",
                submit_exam: "Submit Exam",
                question: "Question",
                previous: "Previous",
                next: "Next",
                results_dashboard: "Results Dashboard",
                results_subtitle: "View and analyze exam performance",
                select_exam: "Select Exam",
                exam: "Exam",
                date_range: "Date Range",
                load_results: "Load Results",
                total_students: "Total Students",
                average_score: "Average Score",
                highest_score: "Highest Score",
                pass_rate: "Pass Rate",
                score_distribution: "Score Distribution",
                question_analysis: "Question Analysis",
                student_results: "Student Results",
                export_csv: "Export CSV",
                export_pdf: "Export PDF",
                name: "Name",
                score: "Score",
                percentage: "Percentage",
                time_taken: "Time Taken",
                status: "Status",
                actions: "Actions",
                student_registration_title: "Student Registration",
                student_registration_subtitle: "Manage student enrollment and registration data",
                manual_registration: "Manual Registration",
                full_name: "Full Name",
                email: "Email",
                phone: "Phone",
                department: "Department",
                year_level: "Year Level",
                add_student: "Add Student",
                clear_form: "Clear Form",
                file_upload_registration: "File Upload Registration",
                upload_student_file: "Upload Student List",
                supported_formats: "Supported formats: CSV, Excel, Word, PDF",
                file_format_guide: "File Format Guide",
                download_sample: "Download Sample CSV",
                download_template: "Download Template",
                registered_students: "Registered Students",
                export_csv: "Export CSV",
                export_excel: "Export Excel",
                clear_all: "Clear All",
                showing: "Showing",
                of: "of",
                students: "students",
                year: "Year"
            },
            ar: {
                app_title: "منصة الامتحانات الإلكترونية",
                app_subtitle: "نظام الامتحانات الإلكترونية",
                nav_teacher_setup: "إعداد المعلم",
                nav_student_registration: "تسجيل الطلاب",
                nav_student_exam: "امتحان الطالب",
                nav_teacher_results: "النتائج",
                teacher_setup_title: "إنشاء امتحان جديد",
                teacher_setup_subtitle: "تصميم امتحانات شاملة بأنواع أسئلة متعددة",
                exam_basic_info: "معلومات الامتحان",
                exam_title: "عنوان الامتحان",
                exam_duration: "المدة (بالدقائق)",
                exam_subject: "المادة",
                exam_total_marks: "إجمالي الدرجات",
                exam_instructions: "التعليمات",
                add_questions: "إضافة أسئلة",
                mcq: "اختيار متعدد",
                short_answer: "إجابة قصيرة",
                fill_blank: "ملء الفراغات",
                figure_naming: "تسمية الأشكال",
                flowchart: "مخطط انسيابي",
                preview_exam: "معاينة الامتحان",
                save_exam: "حفظ الامتحان",
                google_forms: "نماذج جوجل",
                share_link: "رابط المشاركة",
                upload_questions: "رفع الأسئلة",
                student_login: "دخول الطالب",
                enter_credentials: "أدخل بياناتك للوصول إلى الامتحان",
                student_id: "رقم الطالب",
                student_name: "الاسم الكامل",
                exam_code: "رمز الامتحان",
                start_exam: "بدء الامتحان",
                student: "الطالب",
                id: "الرقم",
                submit_exam: "تسليم الامتحان",
                question: "السؤال",
                previous: "السابق",
                next: "التالي",
                results_dashboard: "لوحة النتائج",
                results_subtitle: "عرض وتحليل أداء الامتحان",
                select_exam: "اختر الامتحان",
                exam: "الامتحان",
                date_range: "نطاق التاريخ",
                load_results: "تحميل النتائج",
                total_students: "إجمالي الطلاب",
                average_score: "متوسط الدرجات",
                highest_score: "أعلى درجة",
                pass_rate: "معدل النجاح",
                score_distribution: "توزيع الدرجات",
                question_analysis: "تحليل الأسئلة",
                student_results: "نتائج الطلاب",
                export_csv: "تصدير CSV",
                export_pdf: "تصدير PDF",
                name: "الاسم",
                score: "الدرجة",
                percentage: "النسبة المئوية",
                time_taken: "الوقت المستغرق",
                status: "الحالة",
                actions: "الإجراءات",
                student_registration_title: "تسجيل الطلاب",
                student_registration_subtitle: "إدارة بيانات التسجيل والقيد",
                manual_registration: "التسجيل اليدوي",
                full_name: "الاسم الكامل",
                email: "البريد الإلكتروني",
                phone: "الهاتف",
                department: "القسم",
                year_level: "السنة الدراسية",
                add_student: "إضافة طالب",
                clear_form: "مسح النموذج",
                file_upload_registration: "تسجيل عبر رفع الملف",
                upload_student_file: "رفع قائمة الطلاب",
                supported_formats: "الصيغ المدعومة: CSV، Excel، Word، PDF",
                file_format_guide: "دليل صيغة الملف",
                download_sample: "تحميل نموذج CSV",
                download_template: "تحميل القالب",
                registered_students: "الطلاب المسجلون",
                export_csv: "تصدير CSV",
                export_excel: "تصدير Excel",
                clear_all: "مسح الكل",
                showing: "عرض",
                of: "من",
                students: "طلاب",
                year: "السنة"
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            showView('teacher-setup');
            updateLanguage();
            loadSavedExams();
            initializeStudentRegistration();
        });

        // View management
        function showView(viewName) {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.remove('active');
            });

            // Show selected view
            document.getElementById(viewName).classList.add('active');

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Update current view
            currentView = viewName;

            // Specific view initialization
            if (viewName === 'teacher-results') {
                loadExamOptions();
            } else if (viewName === 'student-registration') {
                loadRegisteredStudents();
            }
        }

        // Language management
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
            updateLanguage();
        }

        function updateLanguage() {
            const htmlRoot = document.getElementById('htmlRoot');
            const currentLangSpan = document.getElementById('currentLang');

            if (currentLanguage === 'ar') {
                htmlRoot.setAttribute('dir', 'rtl');
                htmlRoot.setAttribute('lang', 'ar');
                currentLangSpan.textContent = 'ع';
            } else {
                htmlRoot.setAttribute('dir', 'ltr');
                htmlRoot.setAttribute('lang', 'en');
                currentLangSpan.textContent = 'EN';
            }

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[currentLanguage][key]) {
                    element.textContent = translations[currentLanguage][key];
                }
            });
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('hidden');
        }

        // Question management functions
        function addQuestion(type) {
            questionCounter++;
            const questionId = `question_${questionCounter}`;
            const questionContainer = document.getElementById('questionsContainer');

            let questionHTML = '';

            switch(type) {
                case 'mcq':
                    questionHTML = createMCQQuestion(questionId);
                    break;
                case 'short':
                    questionHTML = createShortAnswerQuestion(questionId);
                    break;
                case 'fillblank':
                    questionHTML = createFillBlankQuestion(questionId);
                    break;
                case 'figure':
                    questionHTML = createFigureQuestion(questionId);
                    break;
                case 'flowchart':
                    questionHTML = createFlowchartQuestion(questionId);
                    break;
            }

            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-container';
            questionDiv.id = questionId;
            questionDiv.innerHTML = questionHTML;

            questionContainer.appendChild(questionDiv);
        }

        function createMCQQuestion(questionId) {
            return `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Multiple Choice Question</h4>
                        <button type="button" onclick="removeQuestion('${questionId}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Enter your question..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Options</label>
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <input type="radio" name="${questionId}_correct" value="0" class="text-blue-600">
                                    <input type="text" placeholder="Option A" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" name="${questionId}_correct" value="1" class="text-blue-600">
                                    <input type="text" placeholder="Option B" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" name="${questionId}_correct" value="2" class="text-blue-600">
                                    <input type="text" placeholder="Option C" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" name="${questionId}_correct" value="3" class="text-blue-600">
                                    <input type="text" placeholder="Option D" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Marks</label>
                                <input type="number" min="1" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="easy">Easy</option>
                                    <option value="medium">Medium</option>
                                    <option value="hard">Hard</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createShortAnswerQuestion(questionId) {
            return `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Short Answer Question</h4>
                        <button type="button" onclick="removeQuestion('${questionId}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Enter your question..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Sample Answer (for reference)</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="2" placeholder="Enter a sample answer..."></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Marks</label>
                                <input type="number" min="1" value="5" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Words</label>
                                <input type="number" min="10" value="100" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createFillBlankQuestion(questionId) {
            return `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Fill in the Blanks</h4>
                        <button type="button" onclick="removeQuestion('${questionId}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Enter your question with _____ for blanks..."></textarea>
                            <p class="text-sm text-gray-500 mt-1">Use _____ (underscores) to indicate blanks</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Correct Answers (one per line)</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Answer 1&#10;Answer 2&#10;Answer 3"></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Marks per Blank</label>
                                <input type="number" min="1" value="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Case Sensitive</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="false">No</option>
                                    <option value="true">Yes</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createFigureQuestion(questionId) {
            return `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Figure/Circuit Naming</h4>
                        <button type="button" onclick="removeQuestion('${questionId}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="2" placeholder="Enter instructions for the figure..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Upload Figure/Circuit</label>
                            <div class="figure-upload-area" onclick="document.getElementById('imageUpload').click()">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600">Click to upload image</p>
                                <p class="text-sm text-gray-400">PNG, JPG, GIF up to 10MB</p>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Parts to Name (one per line)</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="4" placeholder="Part 1: Correct Name&#10;Part 2: Correct Name&#10;Part 3: Correct Name"></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Marks per Part</label>
                                <input type="number" min="1" value="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Total Parts</label>
                                <input type="number" min="1" value="5" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createFlowchartQuestion(questionId) {
            return `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Flowchart Question</h4>
                        <button type="button" onclick="removeQuestion('${questionId}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="2" placeholder="Describe the flowchart task..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Flowchart Canvas</label>
                            <canvas class="flowchart-canvas w-full h-64" id="flowchart_${questionId}"></canvas>
                            <div class="mt-2 flex space-x-2">
                                <button type="button" class="px-3 py-1 bg-blue-600 text-white rounded text-sm">Add Start/End</button>
                                <button type="button" class="px-3 py-1 bg-green-600 text-white rounded text-sm">Add Process</button>
                                <button type="button" class="px-3 py-1 bg-yellow-600 text-white rounded text-sm">Add Decision</button>
                                <button type="button" class="px-3 py-1 bg-red-600 text-white rounded text-sm">Clear</button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Expected Solution (description)</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Describe the expected flowchart solution..."></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Total Marks</label>
                                <input type="number" min="1" value="10" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Time Limit (minutes)</label>
                                <input type="number" min="1" value="15" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function removeQuestion(questionId) {
            const questionElement = document.getElementById(questionId);
            if (questionElement) {
                questionElement.remove();
            }
        }

        // Exam management functions
        function previewExam() {
            const examData = collectExamData();
            if (!examData) return;

            // Create preview modal or new window
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(generateExamPreview(examData));
        }

        function saveExam() {
            const examData = collectExamData();
            if (!examData) return;

            // Save to localStorage
            const savedExams = JSON.parse(localStorage.getItem('savedExams') || '[]');
            examData.id = Date.now().toString();
            examData.createdAt = new Date().toISOString();
            savedExams.push(examData);
            localStorage.setItem('savedExams', JSON.stringify(savedExams));

            alert('Exam saved successfully!');
            loadSavedExams();
        }

        function collectExamData() {
            const title = document.getElementById('examTitle').value;
            const duration = document.getElementById('examDuration').value;
            const subject = document.getElementById('examSubject').value;
            const totalMarks = document.getElementById('examTotalMarks').value;
            const instructions = document.getElementById('examInstructions').value;

            if (!title || !duration || !subject) {
                alert('Please fill in all required exam information.');
                return null;
            }

            const questions = [];
            document.querySelectorAll('.question-container').forEach(container => {
                const questionData = extractQuestionData(container);
                if (questionData) {
                    questions.push(questionData);
                }
            });

            if (questions.length === 0) {
                alert('Please add at least one question.');
                return null;
            }

            return {
                title,
                duration: parseInt(duration),
                subject,
                totalMarks: parseInt(totalMarks),
                instructions,
                questions
            };
        }

        function extractQuestionData(container) {
            // Extract question data based on question type
            // This is a simplified version - you'd need to implement specific extraction for each type
            const questionType = container.querySelector('h4').textContent.toLowerCase();
            const questionText = container.querySelector('textarea').value;

            return {
                type: questionType,
                text: questionText,
                // Add more specific data extraction based on question type
            };
        }

        function generateGoogleForm() {
            const examData = collectExamData();
            if (!examData) return;

            // Generate Google Forms compatible format
            let formData = `Google Forms Export for: ${examData.title}\n\n`;
            formData += `Subject: ${examData.subject}\n`;
            formData += `Duration: ${examData.duration} minutes\n`;
            formData += `Instructions: ${examData.instructions}\n\n`;

            examData.questions.forEach((question, index) => {
                formData += `Question ${index + 1}: ${question.text}\n`;
                // Add question-specific formatting
                formData += '\n';
            });

            // Create downloadable file
            const blob = new Blob([formData], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${examData.title}_GoogleForms.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function generateShareLink() {
            const examData = collectExamData();
            if (!examData) return;

            // Generate a shareable link (in a real application, this would be a server endpoint)
            const examCode = Math.random().toString(36).substring(2, 8).toUpperCase();
            const shareLink = `${window.location.origin}${window.location.pathname}?exam=${examCode}`;

            // Save exam with code
            examData.code = examCode;
            const savedExams = JSON.parse(localStorage.getItem('savedExams') || '[]');
            const existingIndex = savedExams.findIndex(exam => exam.id === examData.id);
            if (existingIndex >= 0) {
                savedExams[existingIndex] = examData;
            } else {
                examData.id = Date.now().toString();
                savedExams.push(examData);
            }
            localStorage.setItem('savedExams', JSON.stringify(savedExams));

            // Show share link modal
            alert(`Share this link with students:\n${shareLink}\nExam Code: ${examCode}`);
        }

        function uploadQuestions() {
            document.getElementById('fileUpload').click();
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    // Process uploaded questions
                    if (data.questions && Array.isArray(data.questions)) {
                        data.questions.forEach(question => {
                            // Add each question to the exam
                            addQuestionFromData(question);
                        });
                        alert('Questions uploaded successfully!');
                    }
                } catch (error) {
                    alert('Error reading file. Please ensure it\'s a valid JSON format.');
                }
            };
            reader.readAsText(file);
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                // Handle image upload for figure questions
                const imageUrl = e.target.result;
                // You would typically upload this to a server and get a URL back
                console.log('Image uploaded:', imageUrl);
            };
            reader.readAsDataURL(file);
        }

        // Student exam functions
        document.getElementById('studentLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const studentId = document.getElementById('studentId').value;
            const studentName = document.getElementById('studentName').value;
            const examCode = document.getElementById('examCode').value;

            if (!studentId || !studentName || !examCode) {
                alert('Please fill in all fields.');
                return;
            }

            // Find exam by code
            const savedExams = JSON.parse(localStorage.getItem('savedExams') || '[]');
            const exam = savedExams.find(e => e.code === examCode);

            if (!exam) {
                alert('Invalid exam code. Please check and try again.');
                return;
            }

            // Start exam
            startExam(exam, { id: studentId, name: studentName });
        });

        function startExam(exam, student) {
            currentExam = exam;
            currentQuestionIndex = 0;
            studentAnswers = {};
            examStartTime = new Date();

            // Hide login, show exam interface
            document.getElementById('studentLogin').style.display = 'none';
            document.getElementById('examInterface').classList.remove('hidden');

            // Set exam info
            document.getElementById('examTitleDisplay').textContent = exam.title;
            document.getElementById('studentNameDisplay').textContent = student.name;
            document.getElementById('studentIdDisplay').textContent = student.id;

            // Setup timer
            startExamTimer(exam.duration);

            // Setup questions
            setupQuestionNavigation(exam.questions);
            showQuestion(0);
        }

        function startExamTimer(durationMinutes) {
            const endTime = new Date(examStartTime.getTime() + durationMinutes * 60000);

            examTimer = setInterval(() => {
                const now = new Date();
                const timeLeft = endTime - now;

                if (timeLeft <= 0) {
                    clearInterval(examTimer);
                    submitExam();
                    return;
                }

                const hours = Math.floor(timeLeft / 3600000);
                const minutes = Math.floor((timeLeft % 3600000) / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);

                const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('timeRemaining').textContent = timeString;

                // Warning when 5 minutes left
                if (timeLeft <= 300000) {
                    document.getElementById('examTimer').classList.add('warning');
                }
            }, 1000);
        }

        function setupQuestionNavigation(questions) {
            const nav = document.getElementById('questionNavigation');
            nav.innerHTML = '';

            questions.forEach((question, index) => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'w-8 h-8 rounded-full border-2 border-gray-300 text-sm font-medium transition-colors';
                button.textContent = index + 1;
                button.onclick = () => showQuestion(index);
                nav.appendChild(button);
            });
        }

        function showQuestion(index) {
            if (!currentExam || !currentExam.questions[index]) return;

            currentQuestionIndex = index;
            const question = currentExam.questions[index];
            const container = document.getElementById('currentQuestion');

            // Update navigation
            document.querySelectorAll('#questionNavigation button').forEach((btn, i) => {
                btn.className = 'w-8 h-8 rounded-full border-2 text-sm font-medium transition-colors';
                if (i === index) {
                    btn.className += ' bg-blue-600 text-white border-blue-600';
                } else if (studentAnswers[i] !== undefined) {
                    btn.className += ' bg-green-100 text-green-800 border-green-300';
                } else {
                    btn.className += ' border-gray-300 text-gray-700 hover:border-blue-300';
                }
            });

            // Render question based on type
            container.innerHTML = renderQuestion(question, index);

            // Load saved answer if exists
            if (studentAnswers[index] !== undefined) {
                loadSavedAnswer(index, studentAnswers[index]);
            }
        }

        function renderQuestion(question, index) {
            const questionNumber = index + 1;
            let html = `
                <div class="mb-6">
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Question ${questionNumber}</h3>
                        <span class="text-sm text-gray-500">${question.marks || 1} marks</span>
                    </div>
                    <p class="text-gray-700 mb-4">${question.text}</p>
                </div>
            `;

            // Add question-specific input based on type
            switch(question.type.toLowerCase()) {
                case 'multiple choice question':
                case 'mcq':
                    html += renderMCQInput(question, index);
                    break;
                case 'short answer question':
                case 'short':
                    html += renderShortAnswerInput(question, index);
                    break;
                case 'fill in the blanks':
                case 'fillblank':
                    html += renderFillBlankInput(question, index);
                    break;
                case 'figure/circuit naming':
                case 'figure':
                    html += renderFigureInput(question, index);
                    break;
                case 'flowchart question':
                case 'flowchart':
                    html += renderFlowchartInput(question, index);
                    break;
                default:
                    html += `<textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg" rows="4" placeholder="Enter your answer..."></textarea>`;
            }

            return html;
        }

        function renderMCQInput(question, index) {
            const options = question.options || ['Option A', 'Option B', 'Option C', 'Option D'];
            let html = '<div class="space-y-3">';

            options.forEach((option, optionIndex) => {
                html += `
                    <label class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input type="radio" name="question_${index}" value="${optionIndex}" class="text-blue-600" onchange="saveAnswer(${index}, ${optionIndex})">
                        <span class="text-gray-700">${option}</span>
                    </label>
                `;
            });

            html += '</div>';
            return html;
        }

        function renderShortAnswerInput(question, index) {
            return `
                <div>
                    <textarea
                        id="answer_${index}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        rows="4"
                        placeholder="Enter your answer..."
                        onchange="saveAnswer(${index}, this.value)"
                        maxlength="${question.maxWords * 10 || 1000}">
                    </textarea>
                    <p class="text-sm text-gray-500 mt-1">Maximum ${question.maxWords || 100} words</p>
                </div>
            `;
        }

        function renderFillBlankInput(question, index) {
            // Parse question text and create inputs for blanks
            const parts = question.text.split('_____');
            let html = '<div class="space-y-2">';

            for (let i = 0; i < parts.length - 1; i++) {
                html += `
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-700">${parts[i]}</span>
                        <input
                            type="text"
                            class="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                            placeholder="Fill blank ${i + 1}"
                            onchange="saveFillBlankAnswer(${index}, ${i}, this.value)">
                        ${i < parts.length - 2 ? '' : `<span class="text-gray-700">${parts[parts.length - 1]}</span>`}
                    </div>
                `;
            }

            html += '</div>';
            return html;
        }

        function renderFigureInput(question, index) {
            return `
                <div class="space-y-4">
                    ${question.imageUrl ? `<img src="${question.imageUrl}" alt="Question Figure" class="max-w-full h-auto border border-gray-300 rounded-lg">` : ''}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        ${Array.from({length: question.totalParts || 5}, (_, i) => `
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Part ${i + 1}:</label>
                                <input
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    placeholder="Name this part"
                                    onchange="saveFigureAnswer(${index}, ${i}, this.value)">
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function renderFlowchartInput(question, index) {
            return `
                <div class="space-y-4">
                    <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                        <p class="text-sm text-gray-600 mb-2">Draw your flowchart below:</p>
                        <canvas id="studentFlowchart_${index}" class="w-full h-64 bg-white border border-gray-200 rounded"></canvas>
                        <div class="mt-2 flex space-x-2">
                            <button type="button" class="px-3 py-1 bg-blue-600 text-white rounded text-sm">Start/End</button>
                            <button type="button" class="px-3 py-1 bg-green-600 text-white rounded text-sm">Process</button>
                            <button type="button" class="px-3 py-1 bg-yellow-600 text-white rounded text-sm">Decision</button>
                            <button type="button" class="px-3 py-1 bg-red-600 text-white rounded text-sm">Clear</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function saveAnswer(questionIndex, answer) {
            studentAnswers[questionIndex] = answer;
            updateQuestionNavigation();
        }

        function saveFillBlankAnswer(questionIndex, blankIndex, answer) {
            if (!studentAnswers[questionIndex]) {
                studentAnswers[questionIndex] = {};
            }
            studentAnswers[questionIndex][blankIndex] = answer;
            updateQuestionNavigation();
        }

        function saveFigureAnswer(questionIndex, partIndex, answer) {
            if (!studentAnswers[questionIndex]) {
                studentAnswers[questionIndex] = {};
            }
            studentAnswers[questionIndex][partIndex] = answer;
            updateQuestionNavigation();
        }

        function loadSavedAnswer(questionIndex, answer) {
            // Load previously saved answer into the current question
            if (typeof answer === 'object') {
                // Handle complex answers (fill blanks, figures)
                Object.keys(answer).forEach(key => {
                    const input = document.querySelector(`input[onchange*="${questionIndex}, ${key}"]`);
                    if (input) input.value = answer[key];
                });
            } else {
                // Handle simple answers (MCQ, short answer)
                const input = document.querySelector(`input[name="question_${questionIndex}"][value="${answer}"]`) ||
                             document.querySelector(`#answer_${questionIndex}`);
                if (input) {
                    if (input.type === 'radio') {
                        input.checked = true;
                    } else {
                        input.value = answer;
                    }
                }
            }
        }

        function updateQuestionNavigation() {
            document.querySelectorAll('#questionNavigation button').forEach((btn, i) => {
                if (studentAnswers[i] !== undefined) {
                    btn.className = btn.className.replace(/bg-\w+-\d+|text-\w+-\d+|border-\w+-\d+/g, '');
                    btn.className += ' bg-green-100 text-green-800 border-green-300';
                }
            });
        }

        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                showQuestion(currentQuestionIndex - 1);
            }
        }

        function nextQuestion() {
            if (currentQuestionIndex < currentExam.questions.length - 1) {
                showQuestion(currentQuestionIndex + 1);
            }
        }

        function submitExam() {
            if (!confirm('Are you sure you want to submit your exam? This action cannot be undone.')) {
                return;
            }

            clearInterval(examTimer);

            const examResult = {
                examId: currentExam.id,
                studentId: document.getElementById('studentIdDisplay').textContent,
                studentName: document.getElementById('studentNameDisplay').textContent,
                answers: studentAnswers,
                startTime: examStartTime,
                endTime: new Date(),
                score: calculateScore()
            };

            // Save result
            const results = JSON.parse(localStorage.getItem('examResults') || '[]');
            results.push(examResult);
            localStorage.setItem('examResults', JSON.stringify(results));

            // Show completion message
            alert(`Exam submitted successfully!\nYour score: ${examResult.score.percentage}%`);

            // Reset to login
            document.getElementById('examInterface').classList.add('hidden');
            document.getElementById('studentLogin').style.display = 'block';
            document.getElementById('studentLoginForm').reset();
        }

        function calculateScore() {
            let totalMarks = 0;
            let earnedMarks = 0;

            currentExam.questions.forEach((question, index) => {
                const marks = question.marks || 1;
                totalMarks += marks;

                if (studentAnswers[index] !== undefined) {
                    // Simplified scoring - in a real app, you'd have more sophisticated scoring
                    if (question.type.toLowerCase().includes('mcq')) {
                        if (studentAnswers[index] === question.correctAnswer) {
                            earnedMarks += marks;
                        }
                    } else {
                        // For other question types, give partial credit
                        earnedMarks += marks * 0.8; // Assume 80% for answered questions
                    }
                }
            });

            return {
                earned: earnedMarks,
                total: totalMarks,
                percentage: Math.round((earnedMarks / totalMarks) * 100)
            };
        }

        // Results management functions
        function loadExamOptions() {
            const examSelect = document.getElementById('examSelect');
            const savedExams = JSON.parse(localStorage.getItem('savedExams') || '[]');

            examSelect.innerHTML = '<option value="">Select an exam...</option>';
            savedExams.forEach(exam => {
                const option = document.createElement('option');
                option.value = exam.id;
                option.textContent = `${exam.title} (${exam.subject})`;
                examSelect.appendChild(option);
            });
        }

        function loadResults() {
            const examId = document.getElementById('examSelect').value;
            if (!examId) {
                alert('Please select an exam first.');
                return;
            }

            const results = JSON.parse(localStorage.getItem('examResults') || '[]');
            const examResults = results.filter(result => result.examId === examId);

            updateStatistics(examResults);
            updateResultsTable(examResults);
            updateCharts(examResults);
        }

        function updateStatistics(results) {
            const totalStudents = results.length;
            const scores = results.map(r => r.score.percentage);
            const averageScore = scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
            const highestScore = scores.length > 0 ? Math.max(...scores) : 0;
            const passRate = scores.length > 0 ? Math.round((scores.filter(s => s >= 60).length / scores.length) * 100) : 0;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('averageScore').textContent = averageScore + '%';
            document.getElementById('highestScore').textContent = highestScore + '%';
            document.getElementById('passRate').textContent = passRate + '%';
        }

        function updateResultsTable(results) {
            const tbody = document.getElementById('resultsTableBody');
            tbody.innerHTML = '';

            results.forEach(result => {
                const row = document.createElement('tr');
                const timeTaken = Math.round((new Date(result.endTime) - new Date(result.startTime)) / 60000);
                const status = result.score.percentage >= 60 ? 'Pass' : 'Fail';
                const statusClass = result.score.percentage >= 60 ? 'text-green-600' : 'text-red-600';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.studentId}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.studentName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.score.earned}/${result.score.total}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.score.percentage}%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timeTaken} min</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${statusClass}">${status}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button type="button" class="text-blue-600 hover:text-blue-900">View Details</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateCharts(results) {
            // Score distribution chart
            const scores = results.map(r => r.score.percentage);
            const scoreRanges = ['0-20', '21-40', '41-60', '61-80', '81-100'];
            const scoreDistribution = scoreRanges.map(range => {
                const [min, max] = range.split('-').map(Number);
                return scores.filter(score => score >= min && score <= max).length;
            });

            const scoreCtx = document.getElementById('scoreChart').getContext('2d');
            new Chart(scoreCtx, {
                type: 'bar',
                data: {
                    labels: scoreRanges,
                    datasets: [{
                        label: 'Number of Students',
                        data: scoreDistribution,
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function exportResults(format) {
            const examId = document.getElementById('examSelect').value;
            if (!examId) {
                alert('Please select an exam and load results first.');
                return;
            }

            const results = JSON.parse(localStorage.getItem('examResults') || '[]');
            const examResults = results.filter(result => result.examId === examId);

            if (format === 'csv') {
                exportToCSV(examResults);
            } else if (format === 'pdf') {
                exportToPDF(examResults);
            }
        }

        function exportToCSV(results) {
            const headers = ['Student ID', 'Name', 'Score', 'Percentage', 'Time Taken', 'Status'];
            const csvContent = [
                headers.join(','),
                ...results.map(result => {
                    const timeTaken = Math.round((new Date(result.endTime) - new Date(result.startTime)) / 60000);
                    const status = result.score.percentage >= 60 ? 'Pass' : 'Fail';
                    return [
                        result.studentId,
                        result.studentName,
                        `${result.score.earned}/${result.score.total}`,
                        `${result.score.percentage}%`,
                        `${timeTaken} min`,
                        status
                    ].join(',');
                })
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'exam_results.csv';
            a.click();
            URL.revokeObjectURL(url);
        }

        function exportToPDF(results) {
            // Simplified PDF export - in a real app, you'd use a library like jsPDF
            alert('PDF export functionality would be implemented with a PDF library like jsPDF.');
        }

        function loadSavedExams() {
            // Load saved exams for various dropdowns and displays
            const savedExams = JSON.parse(localStorage.getItem('savedExams') || '[]');
            console.log('Loaded exams:', savedExams);
        }

        // Student Registration Functions
        function initializeStudentRegistration() {
            loadRegisteredStudents();
            setupStudentRegistrationForm();
            setupFileUploadArea();
        }

        function setupStudentRegistrationForm() {
            const form = document.getElementById('studentRegistrationForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    addStudentManually();
                });
            }
        }

        function setupFileUploadArea() {
            const uploadArea = document.getElementById('studentFileUploadArea');
            if (uploadArea) {
                // Drag and drop functionality
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleStudentFileUpload({ target: { files: files } });
                    }
                });
            }
        }

        function addStudentManually() {
            const studentData = {
                id: document.getElementById('regStudentId').value.trim(),
                name: document.getElementById('regStudentName').value.trim(),
                email: document.getElementById('regStudentEmail').value.trim(),
                phone: document.getElementById('regStudentPhone').value.trim(),
                department: document.getElementById('regStudentDepartment').value,
                year: document.getElementById('regStudentYear').value,
                registrationDate: new Date().toISOString()
            };

            // Validation
            if (!studentData.id || !studentData.name) {
                alert('Student ID and Name are required.');
                return;
            }

            // Check for duplicate ID
            if (registeredStudents.find(s => s.id === studentData.id)) {
                alert('Student ID already exists. Please use a different ID.');
                return;
            }

            // Add student
            registeredStudents.push(studentData);
            saveRegisteredStudents();
            updateStudentsTable();
            clearRegistrationForm();

            alert('Student added successfully!');
        }

        function clearRegistrationForm() {
            document.getElementById('studentRegistrationForm').reset();
        }

        function handleStudentFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const fileName = file.name.toLowerCase();
            const fileExtension = fileName.split('.').pop();

            // Show loading indicator
            showUploadProgress();

            if (fileExtension === 'csv') {
                parseCSVFile(file);
            } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
                parseExcelFile(file);
            } else if (fileExtension === 'doc' || fileExtension === 'docx') {
                parseWordFile(file);
            } else if (fileExtension === 'pdf') {
                parsePDFFile(file);
            } else if (fileExtension === 'txt') {
                parseTextFile(file);
            } else {
                alert('Unsupported file format. Please use CSV, Excel, Word, PDF, or TXT files.');
                hideUploadProgress();
            }
        }

        function showUploadProgress() {
            const uploadArea = document.getElementById('studentFileUploadArea');
            uploadArea.innerHTML = `
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-lg font-medium text-gray-700">Processing file...</p>
                <p class="text-sm text-gray-500">Please wait while we parse your student data</p>
            `;
        }

        function hideUploadProgress() {
            const uploadArea = document.getElementById('studentFileUploadArea');
            uploadArea.innerHTML = `
                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-700" data-translate="upload_student_file">Upload Student List</p>
                <p class="text-sm text-gray-500 mb-4" data-translate="supported_formats">Supported formats: CSV, Excel, Word, PDF</p>
                <div class="flex justify-center space-x-4 text-xs text-gray-400">
                    <span><i class="fas fa-file-csv mr-1"></i>CSV</span>
                    <span><i class="fas fa-file-excel mr-1"></i>Excel</span>
                    <span><i class="fas fa-file-word mr-1"></i>Word</span>
                    <span><i class="fas fa-file-pdf mr-1"></i>PDF</span>
                </div>
            `;
        }

        function parseCSVFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n');
                    const students = [];

                    // Skip header row if it exists
                    const startIndex = lines[0].toLowerCase().includes('id') || lines[0].toLowerCase().includes('name') ? 1 : 0;

                    for (let i = startIndex; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (line) {
                            const columns = line.split(',').map(col => col.trim().replace(/"/g, ''));
                            if (columns.length >= 2) {
                                const student = {
                                    id: columns[0],
                                    name: columns[1],
                                    email: columns[2] || '',
                                    phone: columns[3] || '',
                                    department: columns[4] || '',
                                    year: columns[5] || '',
                                    registrationDate: new Date().toISOString()
                                };

                                if (student.id && student.name) {
                                    students.push(student);
                                }
                            }
                        }
                    }

                    processUploadedStudents(students);
                } catch (error) {
                    alert('Error parsing CSV file. Please check the format.');
                    console.error('CSV parsing error:', error);
                }
                hideUploadProgress();
            };
            reader.readAsText(file);
        }

        function parseTextFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const text = e.target.result;
                    const lines = text.split('\n');
                    const students = [];

                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (line) {
                            // Try to parse different formats
                            const parts = line.split(/[\t,;|]/).map(part => part.trim());
                            if (parts.length >= 2) {
                                const student = {
                                    id: parts[0],
                                    name: parts[1],
                                    email: parts[2] || '',
                                    phone: parts[3] || '',
                                    department: parts[4] || '',
                                    year: parts[5] || '',
                                    registrationDate: new Date().toISOString()
                                };

                                if (student.id && student.name) {
                                    students.push(student);
                                }
                            }
                        }
                    }

                    processUploadedStudents(students);
                } catch (error) {
                    alert('Error parsing text file. Please check the format.');
                    console.error('Text parsing error:', error);
                }
                hideUploadProgress();
            };
            reader.readAsText(file);
        }

        function parseExcelFile(file) {
            // For Excel files, we'll use a simplified approach
            // In a real application, you'd use a library like SheetJS
            alert('Excel file parsing requires additional libraries. Please convert to CSV format for now.');
            hideUploadProgress();
        }

        function parseWordFile(file) {
            // For Word files, we'll use a simplified approach
            // In a real application, you'd use a library like mammoth.js
            alert('Word file parsing requires additional libraries. Please convert to CSV format for now.');
            hideUploadProgress();
        }

        function parsePDFFile(file) {
            // For PDF files, we'll use a simplified approach
            // In a real application, you'd use a library like PDF.js
            alert('PDF file parsing requires additional libraries. Please convert to CSV format for now.');
            hideUploadProgress();
        }

        function processUploadedStudents(students) {
            if (students.length === 0) {
                alert('No valid student data found in the file.');
                return;
            }

            let addedCount = 0;
            let duplicateCount = 0;

            students.forEach(student => {
                // Check for duplicate ID
                if (!registeredStudents.find(s => s.id === student.id)) {
                    registeredStudents.push(student);
                    addedCount++;
                } else {
                    duplicateCount++;
                }
            });

            saveRegisteredStudents();
            updateStudentsTable();

            let message = `Successfully processed ${students.length} students.\n`;
            message += `Added: ${addedCount}\n`;
            if (duplicateCount > 0) {
                message += `Duplicates skipped: ${duplicateCount}`;
            }

            alert(message);
        }

        function loadRegisteredStudents() {
            registeredStudents = JSON.parse(localStorage.getItem('registeredStudents') || '[]');
            filteredStudents = [...registeredStudents];
            updateStudentsTable();
        }

        function saveRegisteredStudents() {
            localStorage.setItem('registeredStudents', JSON.stringify(registeredStudents));
        }

        function updateStudentsTable() {
            const tbody = document.getElementById('studentsTableBody');
            const startIndex = (currentStudentsPage - 1) * studentsPerPage;
            const endIndex = startIndex + studentsPerPage;
            const pageStudents = filteredStudents.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            pageStudents.forEach((student, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.email || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.phone || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${getDepartmentName(student.department)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${getYearName(student.year)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button type="button" onclick="editStudent('${student.id}')" class="text-blue-600 hover:text-blue-900 mr-3">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" onclick="deleteStudent('${student.id}')" class="text-red-600 hover:text-red-900">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateStudentsPagination();
        }

        function getDepartmentName(code) {
            const departments = {
                'BME': 'Biomedical Engineering',
                'CS': 'Computer Science',
                'EE': 'Electrical Engineering',
                'ME': 'Mechanical Engineering',
                'CE': 'Civil Engineering',
                'CHE': 'Chemical Engineering'
            };
            return departments[code] || code || '-';
        }

        function getYearName(year) {
            const years = {
                '1': 'First Year',
                '2': 'Second Year',
                '3': 'Third Year',
                '4': 'Fourth Year',
                '5': 'Fifth Year'
            };
            return years[year] || year || '-';
        }

        function updateStudentsPagination() {
            const total = filteredStudents.length;
            const totalPages = Math.ceil(total / studentsPerPage);
            const startIndex = (currentStudentsPage - 1) * studentsPerPage + 1;
            const endIndex = Math.min(currentStudentsPage * studentsPerPage, total);

            document.getElementById('studentsShowing').textContent = total > 0 ? `${startIndex}-${endIndex}` : '0';
            document.getElementById('studentsTotal').textContent = total;
            document.getElementById('studentsPageInfo').textContent = `Page ${currentStudentsPage} of ${totalPages}`;
        }

        function filterStudents() {
            const searchTerm = document.getElementById('studentSearch').value.toLowerCase();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;

            filteredStudents = registeredStudents.filter(student => {
                const matchesSearch = !searchTerm ||
                    student.id.toLowerCase().includes(searchTerm) ||
                    student.name.toLowerCase().includes(searchTerm) ||
                    (student.email && student.email.toLowerCase().includes(searchTerm));

                const matchesDepartment = !departmentFilter || student.department === departmentFilter;
                const matchesYear = !yearFilter || student.year === yearFilter;

                return matchesSearch && matchesDepartment && matchesYear;
            });

            currentStudentsPage = 1;
            updateStudentsTable();
        }

        function previousStudentsPage() {
            if (currentStudentsPage > 1) {
                currentStudentsPage--;
                updateStudentsTable();
            }
        }

        function nextStudentsPage() {
            const totalPages = Math.ceil(filteredStudents.length / studentsPerPage);
            if (currentStudentsPage < totalPages) {
                currentStudentsPage++;
                updateStudentsTable();
            }
        }

        function editStudent(studentId) {
            const student = registeredStudents.find(s => s.id === studentId);
            if (!student) return;

            // Populate form with student data
            document.getElementById('regStudentId').value = student.id;
            document.getElementById('regStudentName').value = student.name;
            document.getElementById('regStudentEmail').value = student.email || '';
            document.getElementById('regStudentPhone').value = student.phone || '';
            document.getElementById('regStudentDepartment').value = student.department || '';
            document.getElementById('regStudentYear').value = student.year || '';

            // Remove the student from the list (will be re-added when form is submitted)
            deleteStudent(studentId, false);

            // Scroll to form
            document.getElementById('regStudentId').focus();
        }

        function deleteStudent(studentId, confirm = true) {
            if (confirm && !window.confirm('Are you sure you want to delete this student?')) {
                return;
            }

            registeredStudents = registeredStudents.filter(s => s.id !== studentId);
            saveRegisteredStudents();
            loadRegisteredStudents();
        }

        function clearAllStudents() {
            if (!confirm('Are you sure you want to delete all registered students? This action cannot be undone.')) {
                return;
            }

            registeredStudents = [];
            saveRegisteredStudents();
            loadRegisteredStudents();
        }

        function exportStudents(format) {
            if (registeredStudents.length === 0) {
                alert('No students to export.');
                return;
            }

            if (format === 'csv') {
                exportStudentsCSV();
            } else if (format === 'excel') {
                // For Excel export, we'll generate CSV for now
                alert('Excel export will generate CSV format for compatibility.');
                exportStudentsCSV();
            }
        }

        function exportStudentsCSV() {
            const headers = ['Student ID', 'Name', 'Email', 'Phone', 'Department', 'Year', 'Registration Date'];
            const csvContent = [
                headers.join(','),
                ...registeredStudents.map(student => [
                    student.id,
                    `"${student.name}"`,
                    student.email || '',
                    student.phone || '',
                    student.department || '',
                    student.year || '',
                    new Date(student.registrationDate).toLocaleDateString()
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `registered_students_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function downloadSampleCSV() {
            const sampleData = [
                ['Student ID', 'Name', 'Email', 'Phone', 'Department', 'Year'],
                ['BME001', 'Ahmed Mohammed Ali', '<EMAIL>', '+249123456789', 'BME', '3'],
                ['BME002', 'Fatima Hassan Omar', '<EMAIL>', '+249987654321', 'BME', '2'],
                ['CS001', 'Mohammed Ibrahim Yousif', '<EMAIL>', '+249555666777', 'CS', '4'],
                ['EE001', 'Aisha Abdalla Ahmed', '<EMAIL>', '+249444555666', 'EE', '1']
            ];

            const csvContent = sampleData.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'student_registration_sample.csv';
            a.click();
            URL.revokeObjectURL(url);
        }

        function downloadTemplate() {
            const templateData = [
                ['Student ID', 'Name', 'Email', 'Phone', 'Department', 'Year'],
                ['', '', '', '', '', '']
            ];

            const csvContent = templateData.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'student_registration_template.csv';
            a.click();
            URL.revokeObjectURL(url);
        }

        // Check for exam code in URL
        const urlParams = new URLSearchParams(window.location.search);
        const examCode = urlParams.get('exam');
        if (examCode) {
            document.getElementById('examCode').value = examCode;
            showView('student-exam');
        }
    </script>
</body>
</html>