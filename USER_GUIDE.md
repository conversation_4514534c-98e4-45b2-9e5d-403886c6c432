# 📖 ExamPro User Guide

**Complete Guide for Online Exam Platform**

*By Dr. <PERSON> - SUST BME*

---

## 🎯 Quick Start

### For Teachers

1. **Open the Platform**
   - Navigate to `index.html` in your browser
   - Click on "Teacher Setup" in the navigation

2. **Create Your First Exam**
   - Fill in exam details (title, duration, subject)
   - Add questions using the question type buttons
   - Save and generate a share link

3. **Share with Students**
   - Copy the generated exam code
   - Share the link with your students

4. **Monitor Results**
   - Switch to "Results" view
   - Select your exam and load results
   - Export data as needed

### For Students

1. **Access the Exam**
   - Click on "Student Exam" or use the shared link
   - Enter your student ID, name, and exam code

2. **Take the Exam**
   - Read instructions carefully
   - Navigate between questions using the navigation bar
   - Save answers as you go

3. **Submit**
   - Click "Submit Exam" when finished
   - Confirm your submission

---

## 🎓 Teacher's Guide

### Setting Up Exams

#### Basic Information
- **Exam Title**: Clear, descriptive name
- **Duration**: Time limit in minutes
- **Subject**: Course or topic area
- **Total Marks**: Maximum possible score
- **Instructions**: Guidelines for students

#### Question Types

**1. Multiple Choice Questions (MCQ)**
- Add 4 options (A, B, C, D)
- Select the correct answer
- Set marks and difficulty level

**2. Short Answer Questions**
- Provide sample answer for reference
- Set word limit and marks
- Suitable for explanations

**3. Fill in the Blanks**
- Use _____ to indicate blanks
- List correct answers (one per line)
- Choose case sensitivity

**4. Figure/Circuit Naming**
- Upload image (PNG, JPG, GIF)
- Specify parts to be named
- Set marks per part

**5. Flowchart Questions**
- Use built-in drawing tools
- Describe expected solution
- Set time limit for drawing

### Managing Questions

#### Adding Questions
1. Click the appropriate question type button
2. Fill in all required fields
3. Set marking scheme
4. Preview before saving

#### Editing Questions
- Click on any question to modify
- Update content and settings
- Changes are saved automatically

#### Removing Questions
- Click the trash icon on any question
- Confirm deletion

### Exam Configuration

#### Advanced Settings
- **Question Randomization**: Shuffle question order
- **Time Warnings**: Alert students at specific intervals
- **Auto-Submit**: Automatically submit when time expires
- **Partial Credit**: Allow partial marks for incomplete answers

#### Sharing Options
- **Direct Link**: Share URL with embedded exam code
- **Exam Code**: Provide code for manual entry
- **Google Forms**: Export to Google Forms format
- **Question Bank**: Save questions for reuse

### Results Analysis

#### Statistics Overview
- **Total Students**: Number of participants
- **Average Score**: Mean performance
- **Highest Score**: Best performance
- **Pass Rate**: Percentage above passing threshold

#### Detailed Analytics
- **Score Distribution**: Histogram of results
- **Question Analysis**: Performance per question
- **Time Analysis**: Average completion time
- **Difficulty Assessment**: Question effectiveness

#### Export Options
- **CSV Format**: For spreadsheet analysis
- **PDF Reports**: For documentation
- **Individual Reports**: Per-student breakdown

---

## 👨‍🎓 Student's Guide

### Accessing Exams

#### Login Process
1. Enter your **Student ID** (as provided by instructor)
2. Type your **Full Name** (exactly as registered)
3. Input the **Exam Code** (from teacher or link)
4. Click "Start Exam"

#### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Stable internet connection
- JavaScript enabled
- Cookies allowed

### Taking Exams

#### Interface Overview
- **Header**: Shows exam title, your info, and timer
- **Navigation**: Question numbers with status indicators
- **Content Area**: Current question and answer options
- **Controls**: Previous/Next buttons and Submit

#### Question Navigation
- **Green Numbers**: Answered questions
- **Blue Number**: Current question
- **Gray Numbers**: Unanswered questions
- Click any number to jump to that question

#### Answer Types

**Multiple Choice**
- Click the radio button for your choice
- Only one answer allowed per question
- Answer is saved automatically

**Short Answer**
- Type in the text area
- Respect word limits
- Use clear, concise language

**Fill in the Blanks**
- Enter text in each blank field
- Check spelling carefully
- Case sensitivity may apply

**Figure Naming**
- Label each numbered part
- Use precise terminology
- Refer to the uploaded image

**Flowchart Drawing**
- Use provided drawing tools
- Follow logical flow principles
- Save your work frequently

#### Time Management
- **Timer**: Shows remaining time in header
- **Warnings**: Yellow timer indicates 5 minutes left
- **Auto-Submit**: Exam submits automatically when time expires

#### Saving Progress
- Answers are saved automatically
- You can safely navigate between questions
- Progress is preserved if connection is lost

### Submission Process

#### Manual Submission
1. Review all questions using navigation
2. Click "Submit Exam" button
3. Confirm submission in dialog
4. Note your completion time

#### Automatic Submission
- Occurs when timer reaches zero
- All current answers are saved
- Submission confirmation is shown

#### After Submission
- Exam interface is locked
- Score may be displayed immediately
- Return to login screen for next exam

---

## 🌐 Language Support

### Switching Languages
- Click the language toggle in header
- **EN**: English (left-to-right)
- **ع**: Arabic (right-to-left)

### Arabic Interface
- Complete RTL layout
- Arabic fonts and typography
- Culturally appropriate design
- All UI elements translated

### English Interface
- Standard LTR layout
- International typography
- Modern design principles
- Full accessibility support

---

## 🔧 Troubleshooting

### Common Issues

**Login Problems**
- Verify exam code with instructor
- Check spelling of name and ID
- Ensure JavaScript is enabled
- Try refreshing the page

**Timer Issues**
- Check system clock accuracy
- Avoid browser refresh during exam
- Contact instructor if timer seems incorrect

**Answer Not Saving**
- Ensure stable internet connection
- Try clicking in answer field again
- Use different browser if persistent

**Display Problems**
- Update browser to latest version
- Clear browser cache and cookies
- Disable browser extensions
- Try incognito/private mode

### Technical Support
- **Email**: <EMAIL>
- **Phone**: +249912867327 | +966538076790
- Include browser type and error messages

---

## 📱 Mobile Usage

### Responsive Design
- Optimized for tablets and phones
- Touch-friendly interface
- Adaptive layouts
- Gesture support

### Best Practices
- Use landscape orientation for better view
- Ensure stable WiFi connection
- Close other apps to free memory
- Keep device charged

---

**© 2025 Dr. Mohammed Yagoub Esmail - SUST BME**
