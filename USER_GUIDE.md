# 📖 ExamPro User Guide

**Complete Guide for Online Exam Platform**

*By Dr. <PERSON>smail - SUST BME*

---

## 🎯 Quick Start

### For Teachers

1. **Open the Platform**
   - Navigate to `index.html` in your browser
   - Click on "Teacher Setup" in the navigation

2. **Register Students** (Optional but Recommended)
   - Click on "Student Registration" in the navigation
   - Add students manually or upload CSV/Excel files
   - Manage student database with search and filter

3. **Create Your First Exam**
   - Fill in exam details (title, duration, subject)
   - Add questions using the question type buttons
   - Save and generate a share link

4. **Share with Students**
   - Copy the generated exam code
   - Share the link with your students

5. **Monitor Results**
   - Switch to "Results" view
   - Select your exam and load results
   - Export data as needed

### For Students

1. **Access the Exam**
   - Click on "Student Exam" or use the shared link
   - Enter your student ID, name, and exam code

2. **Take the Exam**
   - Read instructions carefully
   - Navigate between questions using the navigation bar
   - Save answers as you go

3. **Submit**
   - Click "Submit Exam" when finished
   - Confirm your submission

---

## 👥 Student Registration Guide

### Overview
The Student Registration system allows teachers to manage student enrollment efficiently through multiple input methods.

### Manual Registration

#### Adding Individual Students
1. Navigate to **Student Registration** view
2. Fill in the registration form:
   - **Student ID**: Unique identifier (required)
   - **Full Name**: Complete student name (required)
   - **Email**: Contact email address
   - **Phone**: Phone number
   - **Department**: Select from dropdown
   - **Year Level**: Academic year (1-5)
3. Click **Add Student** to save
4. Use **Clear Form** to reset fields

#### Form Validation
- Student ID must be unique
- Name and ID are required fields
- Duplicate IDs are automatically detected
- Email format validation

### File Upload Registration

#### Supported Formats
- **CSV Files** (.csv) - Recommended
- **Excel Files** (.xlsx, .xls) - Basic support
- **Word Documents** (.doc, .docx) - Table format
- **PDF Files** (.pdf) - Text extraction
- **Text Files** (.txt) - Delimited format

#### CSV Format Requirements
```
Student ID,Name,Email,Phone,Department,Year
BME001,Ahmed Ali,<EMAIL>,+249123456789,BME,3
CS002,Sara Hassan,<EMAIL>,+249987654321,CS,2
```

#### Upload Process
1. Click the upload area or drag files
2. Select file from your computer
3. Wait for processing confirmation
4. Review imported students
5. Resolve any duplicates or errors

#### File Format Tips
- **Headers**: First row should contain column names
- **Required Columns**: ID and Name minimum
- **Delimiters**: Use commas, tabs, or semicolons
- **Encoding**: UTF-8 recommended for Arabic names

### Student Management

#### Search and Filter
- **Search Box**: Find by ID, name, or email
- **Department Filter**: Filter by specific department
- **Year Filter**: Filter by academic year
- **Real-time Results**: Updates as you type

#### Student Actions
- **Edit**: Click edit icon to modify student data
- **Delete**: Remove individual students
- **Bulk Operations**: Clear all students

#### Pagination
- **Page Size**: 10 students per page
- **Navigation**: Previous/Next buttons
- **Page Info**: Current page and total count

### Data Export

#### Export Options
- **CSV Format**: For spreadsheet applications
- **Excel Format**: Microsoft Excel compatible
- **Filtered Export**: Export current search results

#### Export Process
1. Apply any desired filters
2. Click **Export CSV** or **Export Excel**
3. File downloads automatically
4. Open in preferred application

### Sample Data and Templates

#### Download Sample CSV
- Click **Download Sample CSV**
- Contains example student data
- Shows proper format and structure
- Includes SUST BME students

#### Download Template
- Click **Download Template**
- Empty CSV with correct headers
- Ready for data entry
- Import after filling

### Best Practices

#### Data Management
- **Regular Backups**: Export student data regularly
- **Unique IDs**: Use consistent ID format (e.g., BME001)
- **Complete Information**: Fill all available fields
- **Verification**: Double-check imported data

#### File Preparation
- **Clean Data**: Remove empty rows and columns
- **Consistent Format**: Use same date/phone formats
- **Character Encoding**: Save as UTF-8 for Arabic names
- **File Size**: Keep files under 5MB for best performance

#### Security Considerations
- **Data Privacy**: Handle student information responsibly
- **Access Control**: Limit access to authorized personnel
- **Regular Updates**: Keep student information current
- **Backup Strategy**: Maintain secure data backups

---

## 🎓 Teacher's Guide

### Setting Up Exams

#### Basic Information
- **Exam Title**: Clear, descriptive name
- **Duration**: Time limit in minutes
- **Subject**: Course or topic area
- **Total Marks**: Maximum possible score
- **Instructions**: Guidelines for students

#### Question Types

**1. Multiple Choice Questions (MCQ)**
- Add 4 options (A, B, C, D)
- Select the correct answer
- Set marks and difficulty level

**2. Short Answer Questions**
- Provide sample answer for reference
- Set word limit and marks
- Suitable for explanations

**3. Fill in the Blanks**
- Use _____ to indicate blanks
- List correct answers (one per line)
- Choose case sensitivity

**4. Figure/Circuit Naming**
- Upload image (PNG, JPG, GIF)
- Specify parts to be named
- Set marks per part

**5. Flowchart Questions**
- Use built-in drawing tools
- Describe expected solution
- Set time limit for drawing

### Managing Questions

#### Adding Questions
1. Click the appropriate question type button
2. Fill in all required fields
3. Set marking scheme
4. Preview before saving

#### Editing Questions
- Click on any question to modify
- Update content and settings
- Changes are saved automatically

#### Removing Questions
- Click the trash icon on any question
- Confirm deletion

### Exam Configuration

#### Advanced Settings
- **Question Randomization**: Shuffle question order
- **Time Warnings**: Alert students at specific intervals
- **Auto-Submit**: Automatically submit when time expires
- **Partial Credit**: Allow partial marks for incomplete answers

#### Sharing Options
- **Direct Link**: Share URL with embedded exam code
- **Exam Code**: Provide code for manual entry
- **Google Forms**: Export to Google Forms format
- **Question Bank**: Save questions for reuse

### Results Analysis

#### Statistics Overview
- **Total Students**: Number of participants
- **Average Score**: Mean performance
- **Highest Score**: Best performance
- **Pass Rate**: Percentage above passing threshold

#### Detailed Analytics
- **Score Distribution**: Histogram of results
- **Question Analysis**: Performance per question
- **Time Analysis**: Average completion time
- **Difficulty Assessment**: Question effectiveness

#### Export Options
- **CSV Format**: For spreadsheet analysis
- **PDF Reports**: For documentation
- **Individual Reports**: Per-student breakdown

---

## 👨‍🎓 Student's Guide

### Accessing Exams

#### Login Process
1. Enter your **Student ID** (as provided by instructor)
2. Type your **Full Name** (exactly as registered)
3. Input the **Exam Code** (from teacher or link)
4. Click "Start Exam"

#### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Stable internet connection
- JavaScript enabled
- Cookies allowed

### Taking Exams

#### Interface Overview
- **Header**: Shows exam title, your info, and timer
- **Navigation**: Question numbers with status indicators
- **Content Area**: Current question and answer options
- **Controls**: Previous/Next buttons and Submit

#### Question Navigation
- **Green Numbers**: Answered questions
- **Blue Number**: Current question
- **Gray Numbers**: Unanswered questions
- Click any number to jump to that question

#### Answer Types

**Multiple Choice**
- Click the radio button for your choice
- Only one answer allowed per question
- Answer is saved automatically

**Short Answer**
- Type in the text area
- Respect word limits
- Use clear, concise language

**Fill in the Blanks**
- Enter text in each blank field
- Check spelling carefully
- Case sensitivity may apply

**Figure Naming**
- Label each numbered part
- Use precise terminology
- Refer to the uploaded image

**Flowchart Drawing**
- Use provided drawing tools
- Follow logical flow principles
- Save your work frequently

#### Time Management
- **Timer**: Shows remaining time in header
- **Warnings**: Yellow timer indicates 5 minutes left
- **Auto-Submit**: Exam submits automatically when time expires

#### Saving Progress
- Answers are saved automatically
- You can safely navigate between questions
- Progress is preserved if connection is lost

### Submission Process

#### Manual Submission
1. Review all questions using navigation
2. Click "Submit Exam" button
3. Confirm submission in dialog
4. Note your completion time

#### Automatic Submission
- Occurs when timer reaches zero
- All current answers are saved
- Submission confirmation is shown

#### After Submission
- Exam interface is locked
- Score may be displayed immediately
- Return to login screen for next exam

---

## 🌐 Language Support

### Switching Languages
- Click the language toggle in header
- **EN**: English (left-to-right)
- **ع**: Arabic (right-to-left)

### Arabic Interface
- Complete RTL layout
- Arabic fonts and typography
- Culturally appropriate design
- All UI elements translated

### English Interface
- Standard LTR layout
- International typography
- Modern design principles
- Full accessibility support

---

## 🔧 Troubleshooting

### Common Issues

**Login Problems**
- Verify exam code with instructor
- Check spelling of name and ID
- Ensure JavaScript is enabled
- Try refreshing the page

**Timer Issues**
- Check system clock accuracy
- Avoid browser refresh during exam
- Contact instructor if timer seems incorrect

**Answer Not Saving**
- Ensure stable internet connection
- Try clicking in answer field again
- Use different browser if persistent

**Display Problems**
- Update browser to latest version
- Clear browser cache and cookies
- Disable browser extensions
- Try incognito/private mode

### Technical Support
- **Email**: <EMAIL>
- **Phone**: +249912867327 | +966538076790
- Include browser type and error messages

---

## 📱 Mobile Usage

### Responsive Design
- Optimized for tablets and phones
- Touch-friendly interface
- Adaptive layouts
- Gesture support

### Best Practices
- Use landscape orientation for better view
- Ensure stable WiFi connection
- Close other apps to free memory
- Keep device charged

---

**© 2025 Dr. Mohammed Yagoub Esmail - SUST BME**
