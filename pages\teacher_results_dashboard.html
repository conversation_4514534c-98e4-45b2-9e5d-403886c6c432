<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teacher Results Dashboard - Online Exam Platform</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fonlineexa5436back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface shadow-subtle border-b border-light sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L3 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-13-5z"/>
                            <path d="M14 14h4v2h-4v-2zm0-4h4v2h-4v-2zm0 8h4v2h-4v-2z" fill="white"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-text-primary">ExamPro</h1>
                        <p class="text-sm text-text-secondary">Online Exam Platform</p>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="teacher_exam_setup_dashboard.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-plus-circle mr-2"></i>Create Exam
                    </a>
                    <a href="teacher_results_dashboard.html" class="bg-primary-50 text-primary px-3 py-2 rounded-md text-sm font-medium border border-primary-200">
                        <i class="fas fa-chart-bar mr-2"></i>Results
                    </a>
                    <a href="student_exam_login.html" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-user-graduate mr-2"></i>Student Portal
                    </a>
                </nav>

                <!-- Language Toggle & User Menu -->
                <div class="flex items-center space-x-4">
                    <button id="languageToggle" class="text-text-secondary hover:text-primary transition-colors">
                        <i class="fas fa-globe mr-1"></i>
                        <span id="currentLang">EN</span>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="Teacher Profile" class="w-8 h-8 rounded-full object-cover" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                        <span class="text-sm text-text-secondary">Dr. Ahmed</span>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn" class="text-text-secondary hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="md:hidden hidden bg-surface border-t border-light">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="teacher_exam_setup_dashboard.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-plus-circle mr-2"></i>Create Exam
                </a>
                <a href="teacher_results_dashboard.html" class="bg-primary-50 text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-chart-bar mr-2"></i>Results
                </a>
                <a href="student_exam_login.html" class="text-text-secondary hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-user-graduate mr-2"></i>Student Portal
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-4 lg:gap-8">
            <!-- Main Results Area -->
            <div class="lg:col-span-3 space-y-8">
                <!-- Page Header with Breadcrumb -->
                <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <nav class="flex items-center space-x-2 text-sm text-text-secondary mb-2">
                                <a href="teacher_exam_setup_dashboard.html" class="hover:text-primary transition-colors">Dashboard</a>
                                <i class="fas fa-chevron-right text-xs"></i>
                                <span class="text-primary">Results</span>
                            </nav>
                            <h2 class="text-2xl font-semibold text-text-primary">Exam Results</h2>
                            <p class="text-text-secondary mt-1">Database Systems - Midterm Exam - Chapter 1-5</p>
                        </div>
                        <div class="flex items-center space-x-2 text-sm text-text-secondary">
                            <i class="fas fa-calendar-alt"></i>
                            <span>July 17, 2025</span>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center">
                            <div class="bg-primary-50 p-3 rounded-lg">
                                <i class="fas fa-users text-primary text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-text-secondary">Total Submissions</p>
                                <p class="text-2xl font-semibold text-text-primary">5/5</p>
                                <div class="w-full bg-secondary-200 rounded-full h-2 mt-2">
                                    <div class="bg-primary h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center">
                            <div class="bg-success-50 p-3 rounded-lg">
                                <i class="fas fa-chart-line text-success text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-text-secondary">Average Score</p>
                                <p class="text-2xl font-semibold text-text-primary">85%</p>
                                <p class="text-sm text-success-600 mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+5% from last exam
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center">
                            <div class="bg-accent-50 p-3 rounded-lg">
                                <i class="fas fa-clock text-accent text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-text-secondary">Completion Rate</p>
                                <p class="text-2xl font-semibold text-text-primary">100%</p>
                                <div class="w-full bg-secondary-200 rounded-full h-2 mt-2">
                                    <div class="bg-accent h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center">
                            <div class="bg-warning-50 p-3 rounded-lg">
                                <i class="fas fa-stopwatch text-warning text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-text-secondary">Avg. Time</p>
                                <p class="text-2xl font-semibold text-text-primary">72 min</p>
                                <p class="text-sm text-text-secondary mt-1">of 90 min allowed</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Filters and Controls -->
                <section class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                            <!-- Search -->
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-text-secondary"></i>
                                </div>
                                <input type="text" id="searchInput" placeholder="Search students..." class="form-input pl-10 w-full sm:w-64" />
                            </div>

                            <!-- Score Range Filter -->
                            <select id="scoreFilter" class="form-input">
                                <option value>All Scores</option>
                                <option value="90-100">90-100%</option>
                                <option value="80-89">80-89%</option>
                                <option value="70-79">70-79%</option>
                                <option value="60-69">60-69%</option>
                                <option value="0-59">Below 60%</option>
                            </select>

                            <!-- Status Filter -->
                            <select id="statusFilter" class="form-input">
                                <option value>All Status</option>
                                <option value="completed">Completed</option>
                                <option value="in-progress">In Progress</option>
                                <option value="not-started">Not Started</option>
                            </select>

                            <!-- Reset Filters -->
                            <button id="resetFilters" class="btn-secondary">
                                <i class="fas fa-undo mr-2"></i>Reset
                            </button>
                        </div>

                        <!-- Export Options -->
                        <div class="flex items-center space-x-3">
                            <button id="exportCSV" class="btn-secondary">
                                <i class="fas fa-file-csv mr-2"></i>Export CSV
                            </button>
                            <button id="exportPDF" class="btn-secondary">
                                <i class="fas fa-file-pdf mr-2"></i>Export PDF
                            </button>
                            <button id="bulkActions" class="btn-primary">
                                <i class="fas fa-cog mr-2"></i>Bulk Actions
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Results Table -->
                <section class="bg-surface rounded-lg shadow-subtle border border-light overflow-hidden">
                    <div class="px-6 py-4 border-b border-light">
                        <h3 class="text-lg font-medium text-text-primary">Student Results</h3>
                        <p class="text-sm text-text-secondary mt-1">Click on any row to view detailed answer breakdown</p>
                    </div>

                    <!-- Desktop Table -->
                    <div class="hidden lg:block overflow-x-auto">
                        <table class="min-w-full divide-y divide-secondary-200">
                            <thead class="bg-secondary-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-100 transition-colors" onclick="sortTable('id')">
                                        <div class="flex items-center space-x-1">
                                            <span>Student ID</span>
                                            <i class="fas fa-sort text-xs"></i>
                                        </div>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-100 transition-colors" onclick="sortTable('name')">
                                        <div class="flex items-center space-x-1">
                                            <span>Name</span>
                                            <i class="fas fa-sort text-xs"></i>
                                        </div>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-100 transition-colors" onclick="sortTable('score')">
                                        <div class="flex items-center space-x-1">
                                            <span>Score</span>
                                            <i class="fas fa-sort text-xs"></i>
                                        </div>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-100 transition-colors" onclick="sortTable('percentage')">
                                        <div class="flex items-center space-x-1">
                                            <span>Percentage</span>
                                            <i class="fas fa-sort text-xs"></i>
                                        </div>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-100 transition-colors" onclick="sortTable('time')">
                                        <div class="flex items-center space-x-1">
                                            <span>Submission Time</span>
                                            <i class="fas fa-sort text-xs"></i>
                                        </div>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody" class="bg-surface divide-y divide-secondary-200">
                                <!-- Student 1 -->
                                <tr class="hover:bg-secondary-50 cursor-pointer transition-colors" onclick="toggleRowDetails('row1')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-mono text-text-secondary">ST001</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?w=32&h=32&fit=crop&crop=face" alt="Ahmed Hassan" class="w-8 h-8 rounded-full object-cover mr-3" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                            <span class="text-sm text-text-primary">Ahmed Hassan</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-text-primary">2/2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-success-600">100%</span>
                                            <div class="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                                                <div class="bg-success h-2 rounded-full" style="width: 100%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-text-secondary">65 min</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-primary hover:text-primary-700 mr-3" onclick="event.stopPropagation(); viewDetails('ST001')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary" onclick="event.stopPropagation(); sendNotification('ST001')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Student 2 -->
                                <tr class="hover:bg-secondary-50 cursor-pointer transition-colors" onclick="toggleRowDetails('row2')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-mono text-text-secondary">ST002</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.pixabay.com/photo/2016/11/21/12/42/beard-1845166_1280.jpg?w=32&h=32&fit=crop&crop=face" alt="Fatima Al-Zahra" class="w-8 h-8 rounded-full object-cover mr-3" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                            <span class="text-sm text-text-primary">Fatima Al-Zahra</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-text-primary">1/2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-warning-600">50%</span>
                                            <div class="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                                                <div class="bg-warning h-2 rounded-full" style="width: 50%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-text-secondary">78 min</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-primary hover:text-primary-700 mr-3" onclick="event.stopPropagation(); viewDetails('ST002')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary" onclick="event.stopPropagation(); sendNotification('ST002')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Student 3 -->
                                <tr class="hover:bg-secondary-50 cursor-pointer transition-colors" onclick="toggleRowDetails('row3')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-mono text-text-secondary">ST003</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="Omar Khalil" class="w-8 h-8 rounded-full object-cover mr-3" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                            <span class="text-sm text-text-primary">Omar Khalil</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-text-primary">2/2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-success-600">100%</span>
                                            <div class="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                                                <div class="bg-success h-2 rounded-full" style="width: 100%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-text-secondary">82 min</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-primary hover:text-primary-700 mr-3" onclick="event.stopPropagation(); viewDetails('ST003')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary" onclick="event.stopPropagation(); sendNotification('ST003')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Student 4 -->
                                <tr class="hover:bg-secondary-50 cursor-pointer transition-colors" onclick="toggleRowDetails('row4')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-mono text-text-secondary">ST004</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=32&h=32&fit=crop&crop=face" alt="Layla Mohammed" class="w-8 h-8 rounded-full object-cover mr-3" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                            <span class="text-sm text-text-primary">Layla Mohammed</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-text-primary">1/2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-error-600">50%</span>
                                            <div class="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                                                <div class="bg-error h-2 rounded-full" style="width: 50%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-text-secondary">90 min</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-primary hover:text-primary-700 mr-3" onclick="event.stopPropagation(); viewDetails('ST004')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary" onclick="event.stopPropagation(); sendNotification('ST004')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Student 5 -->
                                <tr class="hover:bg-secondary-50 cursor-pointer transition-colors" onclick="toggleRowDetails('row5')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-mono text-text-secondary">ST005</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.pixabay.com/photo/2016/11/18/19/07/happy-1836445_1280.jpg?w=32&h=32&fit=crop&crop=face" alt="Yusuf Ibrahim" class="w-8 h-8 rounded-full object-cover mr-3" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                            <span class="text-sm text-text-primary">Yusuf Ibrahim</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-text-primary">2/2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-success-600">100%</span>
                                            <div class="ml-2 w-16 bg-secondary-200 rounded-full h-2">
                                                <div class="bg-success h-2 rounded-full" style="width: 100%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-text-secondary">45 min</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-primary hover:text-primary-700 mr-3" onclick="event.stopPropagation(); viewDetails('ST005')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary" onclick="event.stopPropagation(); sendNotification('ST005')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Cards -->
                    <div class="lg:hidden divide-y divide-secondary-200">
                        <div class="p-4 hover:bg-secondary-50 transition-colors cursor-pointer" onclick="toggleMobileDetails('mobile1')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?w=40&h=40&fit=crop&crop=face" alt="Ahmed Hassan" class="w-10 h-10 rounded-full object-cover" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                    <div>
                                        <p class="text-sm font-medium text-text-primary">Ahmed Hassan</p>
                                        <p class="text-xs text-text-secondary">ST001</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-success-600">100%</p>
                                    <p class="text-xs text-text-secondary">2/2 correct</p>
                                </div>
                            </div>
                            <div id="mobile1" class="hidden mt-4 pt-4 border-t border-secondary-200">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <p class="text-text-secondary">Time:</p>
                                        <p class="text-text-primary">65 min</p>
                                    </div>
                                    <div>
                                        <p class="text-text-secondary">Status:</p>
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="p-4 hover:bg-secondary-50 transition-colors cursor-pointer" onclick="toggleMobileDetails('mobile2')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.pixabay.com/photo/2016/11/21/12/42/beard-1845166_1280.jpg?w=40&h=40&fit=crop&crop=face" alt="Fatima Al-Zahra" class="w-10 h-10 rounded-full object-cover" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                                    <div>
                                        <p class="text-sm font-medium text-text-primary">Fatima Al-Zahra</p>
                                        <p class="text-xs text-text-secondary">ST002</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-warning-600">50%</p>
                                    <p class="text-xs text-text-secondary">1/2 correct</p>
                                </div>
                            </div>
                            <div id="mobile2" class="hidden mt-4 pt-4 border-t border-secondary-200">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <p class="text-text-secondary">Time:</p>
                                        <p class="text-text-primary">78 min</p>
                                    </div>
                                    <div>
                                        <p class="text-text-secondary">Status:</p>
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-success-50 text-success-700 rounded-full">
                                            Completed
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Right Sidebar - Statistics -->
            <div class="lg:col-span-1 mt-8 lg:mt-0">
                <div class="sticky top-24 space-y-6">
                    <!-- Score Distribution Chart -->
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-50 p-2 rounded-lg">
                                <i class="fas fa-chart-pie text-primary text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-text-primary">Score Distribution</h3>
                                <p class="text-sm text-text-secondary">Grade breakdown</p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-success rounded-full"></div>
                                    <span class="text-sm text-text-primary">90-100%</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-16 bg-secondary-200 rounded-full h-2">
                                        <div class="bg-success h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                    <span class="text-sm text-text-secondary">3</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-warning rounded-full"></div>
                                    <span class="text-sm text-text-primary">50-89%</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-16 bg-secondary-200 rounded-full h-2">
                                        <div class="bg-warning h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                    <span class="text-sm text-text-secondary">2</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-error rounded-full"></div>
                                    <span class="text-sm text-text-primary">Below 50%</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-16 bg-secondary-200 rounded-full h-2">
                                        <div class="bg-error h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm text-text-secondary">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Analytics -->
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <h3 class="text-lg font-medium text-text-primary mb-4">Performance Analytics</h3>
                        <div class="space-y-4 text-sm">
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Highest Score:</span>
                                <span class="text-text-primary font-medium">100%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Lowest Score:</span>
                                <span class="text-text-primary font-medium">50%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Standard Deviation:</span>
                                <span class="text-text-primary font-medium">22.4</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Median Score:</span>
                                <span class="text-text-primary font-medium">100%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-text-secondary">Pass Rate:</span>
                                <span class="text-success-600 font-medium">100%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                        <h3 class="text-lg font-medium text-text-primary mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button class="btn-primary w-full">
                                <i class="fas fa-download mr-2"></i>Download Report
                            </button>
                            <button class="btn-secondary w-full">
                                <i class="fas fa-envelope mr-2"></i>Email Results
                            </button>
                            <button class="btn-secondary w-full">
                                <i class="fas fa-print mr-2"></i>Print Summary
                            </button>
                            <a href="teacher_exam_setup_dashboard.html" class="btn-secondary w-full text-center block">
                                <i class="fas fa-plus mr-2"></i>Create New Exam
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Detailed View Modal -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-light">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-text-primary">Detailed Answer Breakdown</h3>
                    <button onclick="closeDetailModal()" class="text-text-secondary hover:text-primary">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="detailContent" class="space-y-6">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-surface border-t border-light mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <p class="text-text-secondary text-sm">
                    © 2025 ExamPro - Online Exam Platform. Developed by Dr. Ahmed Al-Rashid
                </p>
                <p class="text-text-secondary text-xs mt-2">
                    Contact: <EMAIL> | Department of Computer Science
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language toggle
        document.getElementById('languageToggle').addEventListener('click', function() {
            const currentLang = document.getElementById('currentLang');
            const isEnglish = currentLang.textContent === 'EN';
            currentLang.textContent = isEnglish ? 'AR' : 'EN';
            document.documentElement.dir = isEnglish ? 'rtl' : 'ltr';
            document.documentElement.lang = isEnglish ? 'ar' : 'en';
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#resultsTableBody tr');
            
            rows.forEach(row => {
                const studentName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const studentId = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
                
                if (studentName.includes(searchTerm) || studentId.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Filter functionality
        document.getElementById('scoreFilter').addEventListener('change', function(e) {
            const filterValue = e.target.value;
            const rows = document.querySelectorAll('#resultsTableBody tr');
            
            rows.forEach(row => {
                if (!filterValue) {
                    row.style.display = '';
                    return;
                }
                
                const percentageText = row.querySelector('td:nth-child(4) span').textContent;
                const percentage = parseInt(percentageText);
                
                let shouldShow = false;
                switch(filterValue) {
                    case '90-100':
                        shouldShow = percentage >= 90;
                        break;
                    case '80-89':
                        shouldShow = percentage >= 80 && percentage < 90;
                        break;
                    case '70-79':
                        shouldShow = percentage >= 70 && percentage < 80;
                        break;
                    case '60-69':
                        shouldShow = percentage >= 60 && percentage < 70;
                        break;
                    case '0-59':
                        shouldShow = percentage < 60;
                        break;
                }
                
                row.style.display = shouldShow ? '' : 'none';
            });
        });

        // Reset filters
        document.getElementById('resetFilters').addEventListener('click', function() {
            document.getElementById('searchInput').value = '';
            document.getElementById('scoreFilter').value = '';
            document.getElementById('statusFilter').value = '';
            
            const rows = document.querySelectorAll('#resultsTableBody tr');
            rows.forEach(row => row.style.display = '');
        });

        // Sort table functionality
        let sortDirection = {};
        
        function sortTable(column) {
            const tbody = document.getElementById('resultsTableBody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Toggle sort direction
            sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';
            
            rows.sort((a, b) => {
                let aValue, bValue;
                
                switch(column) {
                    case 'id':
                        aValue = a.querySelector('td:nth-child(1)').textContent.trim();
                        bValue = b.querySelector('td:nth-child(1)').textContent.trim();
                        break;
                    case 'name':
                        aValue = a.querySelector('td:nth-child(2)').textContent.trim();
                        bValue = b.querySelector('td:nth-child(2)').textContent.trim();
                        break;
                    case 'score':
                        aValue = a.querySelector('td:nth-child(3)').textContent.trim();
                        bValue = b.querySelector('td:nth-child(3)').textContent.trim();
                        break;
                    case 'percentage':
                        aValue = parseInt(a.querySelector('td:nth-child(4) span').textContent);
                        bValue = parseInt(b.querySelector('td:nth-child(4) span').textContent);
                        break;
                    case 'time':
                        aValue = parseInt(a.querySelector('td:nth-child(5)').textContent);
                        bValue = parseInt(b.querySelector('td:nth-child(5)').textContent);
                        break;
                }
                
                if (typeof aValue === 'number') {
                    return sortDirection[column] === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    return sortDirection[column] === 'asc' ? 
                        aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }
            });
            
            // Clear and re-append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        }

        // Toggle row details
        function toggleRowDetails(rowId) {
            // This would expand inline details in a real implementation
            console.log('Toggle details for:', rowId);
        }

        // Toggle mobile details
        function toggleMobileDetails(mobileId) {
            const details = document.getElementById(mobileId);
            details.classList.toggle('hidden');
        }

        // View detailed breakdown
        function viewDetails(studentId) {
            const modal = document.getElementById('detailModal');
            const content = document.getElementById('detailContent');
            
            // Sample detailed content
            content.innerHTML = `
                <div class="bg-secondary-50 rounded-lg p-4 mb-6">
                    <h4 class="font-medium text-text-primary mb-2">Student: ${studentId}</h4>
                    <p class="text-sm text-text-secondary">Exam: Database Systems - Midterm</p>
                </div>
                
                <div class="space-y-4">
                    <div class="border border-light rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h5 class="font-medium text-text-primary">Question 1</h5>
                            <span class="text-sm text-success-600 bg-success-50 px-2 py-1 rounded">Correct</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">What is the primary key in a relational database?</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <span class="text-text-primary">A. A unique identifier for each record</span>
                                <span class="text-success-600">(Selected)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="border border-light rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h5 class="font-medium text-text-primary">Question 2</h5>
                            <span class="text-sm text-success-600 bg-success-50 px-2 py-1 rounded">Correct</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">Which SQL command is used to retrieve data from a database?</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <span class="text-text-primary">A. SELECT</span>
                                <span class="text-success-600">(Selected)</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        // Close detail modal
        function closeDetailModal() {
            const modal = document.getElementById('detailModal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // Send notification
        function sendNotification(studentId) {
            alert(`Notification sent to student ${studentId}`);
        }

        // Export functionality
        document.getElementById('exportCSV').addEventListener('click', function() {
            alert('Exporting results to CSV...');
        });

        document.getElementById('exportPDF').addEventListener('click', function() {
            alert('Exporting results to PDF...');
        });

        // Bulk actions
        document.getElementById('bulkActions').addEventListener('click', function() {
            alert('Bulk actions menu would open here');
        });

        // Close modal when clicking outside
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>