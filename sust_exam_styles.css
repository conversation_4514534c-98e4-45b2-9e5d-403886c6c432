/* ===== CSS CODE FOR SUST ONLINE EXAM PLATFORM ===== */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
}

.header .subtitle {
    color: #7f8c8d;
    font-size: 1.2rem;
}

/* View Management */
.view {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    animation: fadeIn 0.5s ease-in;
}

.view.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Button Styles */
.btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    font-family: 'Cairo', sans-serif;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

/* Section Styles */
.section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-right: 4px solid #667eea;
}

.section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Question Management */
.question-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #e1e8ed;
}

.answer-option {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 5px;
    transition: background-color 0.2s ease;
}

.answer-option:hover {
    background-color: #f8f9fa;
}

.answer-option input[type="radio"] {
    width: auto;
    margin: 0;
}

/* Student Management */
.student-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 10px;
    background: white;
}

.student-item {
    padding: 8px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.student-item:last-child {
    border-bottom: none;
}

/* Timer Styles */
.timer {
    position: fixed;
    top: 20px;
    left: 20px;
    background: #e74c3c;
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    z-index: 1000;
}

.timer.warning {
    background: #f39c12;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Question Navigation */
.question-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.question-counter {
    font-weight: 600;
    color: #2c3e50;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.results-table th,
.results-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e1e8ed;
}

.results-table th {
    background: #667eea;
    color: white;
    font-weight: 600;
}

.results-table tr:hover {
    background: #f8f9fa;
}

/* Score Badges */
.score-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    color: white;
}

.score-excellent { background: #27ae60; }
.score-good { background: #f39c12; }
.score-poor { background: #e74c3c; }

/* Exam Link Display */
.exam-link {
    background: #e8f5e8;
    border: 2px solid #27ae60;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.exam-link h3 {
    color: #27ae60;
    margin-bottom: 10px;
}

.exam-link code {
    background: #27ae60;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1.2rem;
    display: inline-block;
    margin: 10px 0;
}

/* Disabled State */
.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* File Upload */
.file-upload {
    border: 2px dashed #667eea;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.file-upload:hover {
    background-color: #f8f9fa;
}

.file-upload i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .timer {
        position: relative;
        margin-bottom: 20px;
    }
    
    .question-navigation {
        flex-direction: column;
        gap: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .container {
        padding: 10px;
    }
    
    .view {
        padding: 20px;
    }
}

/* Print Styles */
@media print {
    .timer,
    .btn,
    .question-navigation {
        display: none !important;
    }
    
    body {
        background: white;
    }
    
    .view {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
