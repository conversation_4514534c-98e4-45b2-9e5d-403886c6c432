module.exports = {
  content: ["./pages/*.{html,js}", "./index.html", "./js/*.js"],
  theme: {
    extend: {
      colors: {
        // Primary Colors - Deep blue for academic authority
        primary: {
          DEFAULT: "#1e40af", // blue-800
          50: "#eff6ff", // blue-50
          100: "#dbeafe", // blue-100
          500: "#3b82f6", // blue-500
          600: "#2563eb", // blue-600
          700: "#1d4ed8", // blue-700
          800: "#1e40af", // blue-800
          900: "#1e3a8a", // blue-900
        },
        // Secondary Colors - Professional neutrality
        secondary: {
          DEFAULT: "#64748b", // slate-500
          50: "#f8fafc", // slate-50
          100: "#f1f5f9", // slate-100
          200: "#e2e8f0", // slate-200
          300: "#cbd5e1", // slate-300
          400: "#94a3b8", // slate-400
          500: "#64748b", // slate-500
          600: "#475569", // slate-600
          700: "#334155", // slate-700
          800: "#1e293b", // slate-800
          900: "#0f172a", // slate-900
        },
        // Accent Colors - Interactive elements
        accent: {
          DEFAULT: "#0ea5e9", // sky-500
          50: "#f0f9ff", // sky-50
          100: "#e0f2fe", // sky-100
          500: "#0ea5e9", // sky-500
          600: "#0284c7", // sky-600
        },
        // Background Colors
        background: "#f8fafc", // slate-50
        surface: "#ffffff", // white
        // Text Colors
        text: {
          primary: "#0f172a", // slate-900
          secondary: "#475569", // slate-600
        },
        // Status Colors
        success: {
          DEFAULT: "#059669", // emerald-600
          50: "#ecfdf5", // emerald-50
          100: "#d1fae5", // emerald-100
          500: "#10b981", // emerald-500
          600: "#059669", // emerald-600
        },
        warning: {
          DEFAULT: "#d97706", // amber-600
          50: "#fffbeb", // amber-50
          100: "#fef3c7", // amber-100
          500: "#f59e0b", // amber-500
          600: "#d97706", // amber-600
        },
        error: {
          DEFAULT: "#dc2626", // red-600
          50: "#fef2f2", // red-50
          100: "#fee2e2", // red-100
          500: "#ef4444", // red-500
          600: "#dc2626", // red-600
        },
      },
      fontFamily: {
        // Headings - Modern geometric sans-serif
        sans: ['Inter', 'sans-serif'],
        // Body - Optimized for extended reading
        body: ['Source Sans Pro', 'sans-serif'],
        // Captions - Consistent with headings
        caption: ['Inter', 'sans-serif'],
        // Data - Monospace for alignment
        mono: ['JetBrains Mono', 'monospace'],
      },
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
      },
      boxShadow: {
        'subtle': '0 1px 3px rgba(0, 0, 0, 0.1)',
        'elevated': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'modal': '0 10px 15px rgba(0, 0, 0, 0.1)',
      },
      borderColor: {
        'light': '#e2e8f0', // slate-200
        'medium': '#cbd5e1', // slate-300
      },
      transitionDuration: {
        'fast': '200ms',
        'normal': '300ms',
      },
      transitionTimingFunction: {
        'ease-out': 'ease-out',
      },
      animation: {
        'fade-in': 'fadeIn 200ms ease-out',
        'slide-down': 'slideDown 300ms ease-out',
        'slide-up': 'slideUp 300ms ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      maxWidth: {
        'reading': '65ch', // Optimal reading line length
      },
      screens: {
        'xs': '475px',
      },
    },
  },
  plugins: [
    // Custom plugin for focus-visible support
    function({ addUtilities }) {
      addUtilities({
        '.focus-visible:focus-visible': {
          outline: '2px solid #0ea5e9',
          outlineOffset: '2px',
        },
        '.focus-visible:focus:not(:focus-visible)': {
          outline: 'none',
        },
      });
    },
  ],
};