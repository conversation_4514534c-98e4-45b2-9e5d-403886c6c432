<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة الامتحانات الإلكترونية - جامعة السودان للعلوم والتكنولوجيا</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS Code */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
        }

        .view {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeIn 0.5s ease-in;
        }

        .view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            font-family: 'Cairo', sans-serif;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #667eea;
        }

        .section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #e1e8ed;
        }

        .answer-option {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            transition: background-color 0.2s ease;
        }

        .answer-option:hover {
            background-color: #f8f9fa;
        }

        .answer-option input[type="radio"] {
            width: auto;
            margin: 0;
        }

        .student-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }

        .student-item {
            padding: 8px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .student-item:last-child {
            border-bottom: none;
        }

        .timer {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #e74c3c;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            z-index: 1000;
        }

        .timer.warning {
            background: #f39c12;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .question-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .question-counter {
            font-weight: 600;
            color: #2c3e50;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .results-table th,
        .results-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e1e8ed;
        }

        .results-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .results-table tr:hover {
            background: #f8f9fa;
        }

        .score-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 600;
            color: white;
        }

        .score-excellent { background: #27ae60; }
        .score-good { background: #f39c12; }
        .score-poor { background: #e74c3c; }

        .exam-link {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .exam-link h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .exam-link code {
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 1.2rem;
            display: inline-block;
            margin: 10px 0;
        }

        .disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        .file-upload {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .file-upload:hover {
            background-color: #f8f9fa;
        }

        .file-upload i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .timer {
                position: relative;
                margin-bottom: 20px;
            }
            
            .question-navigation {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-graduation-cap"></i> منصة الامتحانات الإلكترونية</h1>
            <p class="subtitle">جامعة السودان للعلوم والتكنولوجيا - SUST</p>
        </div>

        <!-- Teacher Setup View -->
        <div id="teacherSetupView" class="view active">
            <h2><i class="fas fa-cog"></i> إعداد الامتحان</h2>
            
            <!-- Exam Information Section -->
            <div class="section">
                <h3><i class="fas fa-info-circle"></i> معلومات الامتحان</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="collegeSelect">الكلية:</label>
                        <select id="collegeSelect" required>
                            <option value="">اختر الكلية</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="majorSelect">التخصص:</label>
                        <select id="majorSelect" disabled required>
                            <option value="">اختر التخصص</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="courseName">اسم المقرر:</label>
                        <input type="text" id="courseName" required placeholder="مثال: الرياضيات المتقدمة">
                    </div>
                    <div class="form-group">
                        <label for="examTitle">عنوان الامتحان:</label>
                        <input type="text" id="examTitle" required placeholder="مثال: الامتحان النهائي">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="examDate">تاريخ الامتحان:</label>
                        <input type="date" id="examDate" required>
                    </div>
                    <div class="form-group">
                        <label for="examDuration">مدة الامتحان (بالدقائق):</label>
                        <input type="number" id="examDuration" required min="1" placeholder="60">
                    </div>
                </div>
            </div>

            <!-- Student Management Section -->
            <div class="section">
                <h3><i class="fas fa-users"></i> إدارة الطلاب</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="studentInput">إضافة طالب يدوياً:</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="studentIdInput" placeholder="رقم الطالب" style="flex: 1;">
                            <input type="text" id="studentNameInput" placeholder="اسم الطالب" style="flex: 2;">
                            <button type="button" class="btn" onclick="addStudent()">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>رفع ملف الطلاب (CSV):</label>
                        <div class="file-upload" onclick="document.getElementById('csvFile').click()">
                            <i class="fas fa-upload"></i>
                            <p>اضغط لرفع ملف CSV</p>
                            <small>الصيغة: رقم الطالب، اسم الطالب</small>
                        </div>
                        <input type="file" id="csvFile" accept=".csv" style="display: none;" onchange="handleCSVUpload(event)">
                    </div>
                </div>
                
                <div class="student-list" id="studentList">
                    <p style="text-align: center; color: #7f8c8d;">لم يتم إضافة طلاب بعد</p>
                </div>
            </div>

            <!-- Question Management Section -->
            <div class="section">
                <h3><i class="fas fa-question-circle"></i> إدارة الأسئلة</h3>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="shuffleAnswers" style="width: auto; margin-left: 10px;">
                        خلط ترتيب الإجابات لكل طالب
                    </label>
                </div>
                
                <div id="questionsList"></div>
                
                <button type="button" class="btn btn-secondary" onclick="addQuestion()">
                    <i class="fas fa-plus"></i> إضافة سؤال جديد
                </button>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin-top: 30px;">
                <button type="button" class="btn btn-success" onclick="createExam()" style="font-size: 1.2rem; padding: 15px 30px;">
                    <i class="fas fa-rocket"></i> إنشاء الامتحان وتوليد الرابط
                </button>
            </div>
        </div>

        <!-- Student Exam View -->
        <div id="studentExamView" class="view">
            <!-- Timer -->
            <div id="examTimer" class="timer">
                <i class="fas fa-clock"></i> <span id="timerDisplay">00:00</span>
            </div>

            <!-- Student Login -->
            <div id="studentLogin">
                <h2><i class="fas fa-sign-in-alt"></i> دخول الطالب</h2>
                <div class="form-group">
                    <label for="studentIdLogin">رقم الطالب:</label>
                    <input type="text" id="studentIdLogin" placeholder="أدخل رقم الطالب">
                </div>
                <button type="button" class="btn" onclick="startExam()">
                    <i class="fas fa-play"></i> بدء الامتحان
                </button>
            </div>

            <!-- Exam Interface -->
            <div id="examInterface" style="display: none;">
                <h2 id="examHeader"></h2>
                
                <div class="question-navigation">
                    <button type="button" class="btn btn-secondary" id="prevBtn" onclick="previousQuestion()">
                        <i class="fas fa-arrow-right"></i> السابق
                    </button>
                    <span class="question-counter" id="questionCounter"></span>
                    <button type="button" class="btn" id="nextBtn" onclick="nextQuestion()">
                        التالي <i class="fas fa-arrow-left"></i>
                    </button>
                </div>

                <div id="currentQuestion" class="question-item"></div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="button" class="btn btn-danger" onclick="submitExam()">
                        <i class="fas fa-paper-plane"></i> تسليم الامتحان
                    </button>
                </div>
            </div>
        </div>

        <!-- Teacher Results View -->
        <div id="teacherResultsView" class="view">
            <h2><i class="fas fa-chart-bar"></i> نتائج الامتحان</h2>
            
            <div class="section">
                <h3><i class="fas fa-trophy"></i> إحصائيات عامة</h3>
                <div class="form-row">
                    <div style="text-align: center; padding: 20px; background: white; border-radius: 8px;">
                        <h4>عدد الطلاب المشاركين</h4>
                        <span id="totalStudents" style="font-size: 2rem; color: #667eea; font-weight: bold;">0</span>
                    </div>
                    <div style="text-align: center; padding: 20px; background: white; border-radius: 8px;">
                        <h4>متوسط الدرجات</h4>
                        <span id="averageScore" style="font-size: 2rem; color: #27ae60; font-weight: bold;">0%</span>
                    </div>
                </div>
            </div>

            <table class="results-table" id="resultsTable">
                <thead>
                    <tr>
                        <th>رقم الطالب</th>
                        <th>اسم الطالب</th>
                        <th>الدرجة</th>
                        <th>النسبة المئوية</th>
                        <th>وقت التسليم</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                </tbody>
            </table>

            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="showTeacherSetup()">
                    <i class="fas fa-arrow-right"></i> العودة لإعداد امتحان جديد
                </button>
            </div>
        </div>

        <!-- Exam Link Display -->
        <div id="examLinkDisplay" class="exam-link" style="display: none;">
            <h3><i class="fas fa-link"></i> تم إنشاء الامتحان بنجاح!</h3>
            <p>شارك هذا الرابط مع الطلاب:</p>
            <code id="examLinkCode"></code>
            <br>
            <button type="button" class="btn" onclick="showStudentView()">
                <i class="fas fa-eye"></i> معاينة واجهة الطالب
            </button>
            <button type="button" class="btn btn-secondary" onclick="showResultsView()">
                <i class="fas fa-chart-bar"></i> عرض النتائج
            </button>
        </div>
    </div>

    <script>
        // ===== DATA STRUCTURES AND GLOBAL VARIABLES =====

        // SUST Colleges and Majors Data Structure - Complete Updated Version
        const sustData = {
            "كلية الهندسة": [
                "مدرسة الهندسة المدنية - هندسة الإنشاءات",
                "مدرسة الهندسة المدنية - هندسة الموارد المائية والبيئية",
                "مدرسة الهندسة المدنية - هندسة النقل والطرق",
                "مدرسة الهندسة المدنية - إدارة التشييد",
                "مدرسة الهندسة الميكانيكية - هندسة القوى والحراريات",
                "مدرسة الهندسة الميكانيكية - التصميم والإنتاج",
                "مدرسة الهندسة الميكانيكية - الهندسة الصناعية",
                "مدرسة الهندسة الميكانيكية - الميكاترونكس",
                "مدرسة هندسة الكهرباء - هندسة القدرة والآلات الكهربائية",
                "مدرسة هندسة الكهرباء - هندسة التحكم",
                "مدرسة هندسة المساحة - الجيوديسيا",
                "مدرسة هندسة المساحة - التصوير المساحي والاستشعار عن بعد",
                "مدرسة هندسة المساحة - نظم المعلومات الجغرافية",
                "مدرسة هندسة الإلكترونيات - هندسة الاتصالات",
                "مدرسة هندسة الإلكترونيات - الأنظمة المدمجة",
                "مدرسة هندسة الإلكترونيات - هندسة الحاسوب والشبكات",
                "قسم الهندسة الطبية الحيوية",
                "قسم هندسة الطيران"
            ],
            "كلية هندسة العمارة والتخطيط العمراني": ["هندسة العمارة", "التخطيط العمراني"],
            "كلية الهندسة الصناعية الكيميائية": ["الهندسة الكيميائية", "هندسة العمليات الصناعية", "الصناعات البتروكيماوية", "الصناعات الدوائية", "الصناعات الغذائية"],
            "كلية هندسة النفط": ["هندسة استكشاف النفط", "هندسة استخراج النفط", "هندسة إنتاج النفط والغاز"],
            "كلية هندسة المياه (ود المقبول)": ["هندسة الموارد المائية", "هندسة معالجة المياه", "هندسة الري والصرف"],
            "كلية الطب البشري": ["الطب العام", "الجراحة العامة", "الطب الباطني", "طب الأطفال", "النساء والتوليد", "طب المجتمع"],
            "كلية الصيدلة": ["الصيدلة الإكلينيكية", "الكيمياء الصيدلانية", "علم الأدوية", "الصيدلانيات"],
            "كلية طب الأسنان": ["طب الأسنان العام", "جراحة الفم والأسنان", "تقويم الأسنان", "طب أسنان الأطفال"],
            "كلية علوم الحاسوب وتقنية المعلومات": ["علوم الحاسوب", "تقنية المعلومات", "نظم المعلومات"],
            "كلية العلوم": ["الفيزياء", "الكيمياء", "الرياضيات", "علوم الأرض (الجيولوجيا)"],
            "كلية الدراسات التجارية": ["المحاسبة", "إدارة الأعمال", "التسويق", "التمويل والمصارف"],
            "كلية اللغات": ["اللغة الإنجليزية", "اللغة الفرنسية", "اللغة العربية"],
            "كلية الفنون الجميلة والتطبيقية": ["التصميم الصناعي", "التصميم الإيضاحي", "الطباعة والنشر", "الخزف", "النسيج"],
            "كلية التربية": ["التربية التقنية", "التربية الأسرية", "التربية الفنية"],
            "كلية الموسيقى والدراما": ["الموسيقى", "الدراما"],
            "كلية تكنولوجيا النفط والغاز": ["تكنولوجيا تشغيل المصافي", "تكنولوجيا إنتاج النفط والغاز"],
            "كلية علوم الاتصال": ["العلاقات العامة والإعلان", "الصحافة والنشر الإلكتروني", "الإذاعة والتلفزيون"],
            "كلية الزراعة": ["الإنتاج النباتي", "علوم وتكنولوجيا الأغذية", "الهندسة الزراعية"],
            "كلية الطب البيطري": ["الطب البيطري"],
            "كلية الغابات والمراعي": ["علوم الغابات", "علوم المراعي"]
        };

        // Global Application State
        let examConfig = {
            college: '',
            major: '',
            courseName: '',
            examTitle: '',
            examDate: '',
            duration: 0,
            students: [],
            questions: [],
            shuffleAnswers: false
        };

        let currentStudent = null;
        let currentQuestionIndex = 0;
        let studentAnswers = [];
        let examTimer = null;
        let timeRemaining = 0;
        let examResults = [];

        // ===== INITIALIZATION =====

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeCollegeDropdown();
            setupEventListeners();
        });

        // Initialize college dropdown with SUST data
        function initializeCollegeDropdown() {
            const collegeSelect = document.getElementById('collegeSelect');

            // Clear existing options except the first one
            collegeSelect.innerHTML = '<option value="">اختر الكلية</option>';

            // Populate with SUST colleges
            Object.keys(sustData).forEach(college => {
                const option = document.createElement('option');
                option.value = college;
                option.textContent = college;
                collegeSelect.appendChild(option);
            });
        }

        // Setup event listeners for dependent dropdowns
        function setupEventListeners() {
            const collegeSelect = document.getElementById('collegeSelect');
            const majorSelect = document.getElementById('majorSelect');

            // College selection change handler - implements dependent dropdown logic
            collegeSelect.addEventListener('change', function() {
                const selectedCollege = this.value;

                // Clear and disable major dropdown
                majorSelect.innerHTML = '<option value="">اختر التخصص</option>';

                if (selectedCollege) {
                    // Enable major dropdown
                    majorSelect.disabled = false;

                    // Populate majors for selected college
                    const majors = sustData[selectedCollege];
                    majors.forEach(major => {
                        const option = document.createElement('option');
                        option.value = major;
                        option.textContent = major;
                        majorSelect.appendChild(option);
                    });
                } else {
                    // Disable major dropdown if no college selected
                    majorSelect.disabled = true;
                }
            });
        }

        // ===== STUDENT MANAGEMENT FUNCTIONS =====

        // Add student manually
        function addStudent() {
            const studentId = document.getElementById('studentIdInput').value.trim();
            const studentName = document.getElementById('studentNameInput').value.trim();

            if (!studentId || !studentName) {
                alert('يرجى إدخال رقم الطالب والاسم');
                return;
            }

            // Check for duplicate student ID
            if (examConfig.students.find(s => s.id === studentId)) {
                alert('رقم الطالب موجود مسبقاً');
                return;
            }

            // Add student to the list
            examConfig.students.push({
                id: studentId,
                name: studentName
            });

            // Clear input fields
            document.getElementById('studentIdInput').value = '';
            document.getElementById('studentNameInput').value = '';

            // Update student list display
            updateStudentList();
        }

        // Handle CSV file upload for bulk student addition
        function handleCSVUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                parseCSVData(csv);
            };
            reader.readAsText(file);
        }

        // Parse CSV data and add students
        function parseCSVData(csvData) {
            const lines = csvData.split('\n');
            let addedCount = 0;
            let duplicateCount = 0;

            lines.forEach((line, index) => {
                // Skip empty lines
                if (!line.trim()) return;

                const [studentId, studentName] = line.split(',').map(item => item.trim());

                if (studentId && studentName) {
                    // Check for duplicates
                    if (!examConfig.students.find(s => s.id === studentId)) {
                        examConfig.students.push({
                            id: studentId,
                            name: studentName
                        });
                        addedCount++;
                    } else {
                        duplicateCount++;
                    }
                }
            });

            updateStudentList();
            alert(`تم إضافة ${addedCount} طالب. تم تجاهل ${duplicateCount} طالب مكرر.`);
        }

        // Update student list display
        function updateStudentList() {
            const studentList = document.getElementById('studentList');

            if (examConfig.students.length === 0) {
                studentList.innerHTML = '<p style="text-align: center; color: #7f8c8d;">لم يتم إضافة طلاب بعد</p>';
                return;
            }

            let html = '';
            examConfig.students.forEach((student, index) => {
                html += `
                    <div class="student-item">
                        <span><strong>${student.id}</strong> - ${student.name}</span>
                        <button type="button" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="removeStudent(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            });

            studentList.innerHTML = html;
        }

        // Remove student from list
        function removeStudent(index) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
                examConfig.students.splice(index, 1);
                updateStudentList();
            }
        }

        // ===== QUESTION MANAGEMENT FUNCTIONS =====

        // Add new question
        function addQuestion() {
            const questionIndex = examConfig.questions.length;

            const questionObj = {
                id: questionIndex,
                text: '',
                options: ['', '', '', ''],
                correctAnswer: 0
            };

            examConfig.questions.push(questionObj);
            renderQuestionForm(questionIndex);
        }

        // Render question form
        function renderQuestionForm(questionIndex) {
            const questionsList = document.getElementById('questionsList');

            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-item';
            questionDiv.id = `question-${questionIndex}`;

            questionDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4>السؤال ${questionIndex + 1}</h4>
                    <button type="button" class="btn btn-danger" onclick="removeQuestion(${questionIndex})" style="padding: 5px 10px;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>

                <div class="form-group">
                    <label>نص السؤال:</label>
                    <textarea id="questionText-${questionIndex}" rows="3" placeholder="اكتب السؤال هنا..." onchange="updateQuestion(${questionIndex})"></textarea>
                </div>

                <div class="form-group">
                    <label>الخيارات:</label>
                    ${[0, 1, 2, 3].map(optionIndex => `
                        <div class="answer-option">
                            <input type="radio" name="correct-${questionIndex}" value="${optionIndex}"
                                   ${optionIndex === 0 ? 'checked' : ''}
                                   onchange="updateCorrectAnswer(${questionIndex}, ${optionIndex})">
                            <input type="text" id="option-${questionIndex}-${optionIndex}"
                                   placeholder="الخيار ${['أ', 'ب', 'ج', 'د'][optionIndex]}"
                                   onchange="updateQuestion(${questionIndex})" style="flex: 1;">
                        </div>
                    `).join('')}
                </div>
            `;

            questionsList.appendChild(questionDiv);
        }

        // Update question data
        function updateQuestion(questionIndex) {
            const question = examConfig.questions[questionIndex];

            // Update question text
            question.text = document.getElementById(`questionText-${questionIndex}`).value;

            // Update options
            for (let i = 0; i < 4; i++) {
                question.options[i] = document.getElementById(`option-${questionIndex}-${i}`).value;
            }
        }

        // Update correct answer
        function updateCorrectAnswer(questionIndex, optionIndex) {
            examConfig.questions[questionIndex].correctAnswer = optionIndex;
        }

        // Remove question
        function removeQuestion(questionIndex) {
            if (confirm('هل أنت متأكد من حذف هذا السؤال؟')) {
                examConfig.questions.splice(questionIndex, 1);

                // Re-render all questions with updated indices
                const questionsList = document.getElementById('questionsList');
                questionsList.innerHTML = '';

                examConfig.questions.forEach((_, index) => {
                    renderQuestionForm(index);

                    // Restore question data
                    const question = examConfig.questions[index];
                    document.getElementById(`questionText-${index}`).value = question.text;

                    for (let i = 0; i < 4; i++) {
                        document.getElementById(`option-${index}-${i}`).value = question.options[i];
                    }

                    document.querySelector(`input[name="correct-${index}"][value="${question.correctAnswer}"]`).checked = true;
                });
            }
        }

        // ===== EXAM CREATION AND MANAGEMENT =====

        // Create exam and generate link
        function createExam() {
            // Validate exam configuration
            if (!validateExamConfig()) {
                return;
            }

            // Save exam configuration
            examConfig.college = document.getElementById('collegeSelect').value;
            examConfig.major = document.getElementById('majorSelect').value;
            examConfig.courseName = document.getElementById('courseName').value;
            examConfig.examTitle = document.getElementById('examTitle').value;
            examConfig.examDate = document.getElementById('examDate').value;
            examConfig.duration = parseInt(document.getElementById('examDuration').value);
            examConfig.shuffleAnswers = document.getElementById('shuffleAnswers').checked;

            // Generate unique exam link (simulated)
            const examId = generateExamId();
            const examLink = `${window.location.origin}${window.location.pathname}?exam=${examId}`;

            // Display exam link
            document.getElementById('examLinkCode').textContent = examLink;
            document.getElementById('examLinkDisplay').style.display = 'block';

            // Hide setup view
            document.getElementById('teacherSetupView').classList.remove('active');

            alert('تم إنشاء الامتحان بنجاح!');
        }

        // Validate exam configuration
        function validateExamConfig() {
            const college = document.getElementById('collegeSelect').value;
            const major = document.getElementById('majorSelect').value;
            const courseName = document.getElementById('courseName').value;
            const examTitle = document.getElementById('examTitle').value;
            const examDate = document.getElementById('examDate').value;
            const duration = document.getElementById('examDuration').value;

            if (!college || !major || !courseName || !examTitle || !examDate || !duration) {
                alert('يرجى ملء جميع معلومات الامتحان');
                return false;
            }

            if (examConfig.students.length === 0) {
                alert('يرجى إضافة طلاب للامتحان');
                return false;
            }

            if (examConfig.questions.length === 0) {
                alert('يرجى إضافة أسئلة للامتحان');
                return false;
            }

            // Validate questions
            for (let i = 0; i < examConfig.questions.length; i++) {
                const question = examConfig.questions[i];
                if (!question.text.trim()) {
                    alert(`يرجى إدخال نص السؤال رقم ${i + 1}`);
                    return false;
                }

                for (let j = 0; j < 4; j++) {
                    if (!question.options[j].trim()) {
                        alert(`يرجى إدخال جميع خيارات السؤال رقم ${i + 1}`);
                        return false;
                    }
                }
            }

            return true;
        }

        // Generate unique exam ID
        function generateExamId() {
            return 'SUST-' + Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // ===== FISHER-YATES SHUFFLE ALGORITHM =====

        // Shuffle array using Fisher-Yates algorithm
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }

        // Shuffle question options for a student
        function shuffleQuestionOptions(questions) {
            return questions.map(question => {
                if (!examConfig.shuffleAnswers) {
                    return {
                        ...question,
                        shuffledOptions: question.options,
                        shuffledCorrectAnswer: question.correctAnswer
                    };
                }

                // Create array of option indices
                const indices = [0, 1, 2, 3];
                const shuffledIndices = shuffleArray(indices);

                // Create shuffled options
                const shuffledOptions = shuffledIndices.map(index => question.options[index]);

                // Find new position of correct answer
                const shuffledCorrectAnswer = shuffledIndices.indexOf(question.correctAnswer);

                return {
                    ...question,
                    shuffledOptions,
                    shuffledCorrectAnswer
                };
            });
        }

        // ===== STUDENT EXAM INTERFACE =====

        // Show student exam view
        function showStudentView() {
            document.getElementById('examLinkDisplay').style.display = 'none';
            document.getElementById('studentExamView').classList.add('active');
            document.getElementById('studentLogin').style.display = 'block';
            document.getElementById('examInterface').style.display = 'none';
        }

        // Start exam for student
        function startExam() {
            const studentId = document.getElementById('studentIdLogin').value.trim();

            if (!studentId) {
                alert('يرجى إدخال رقم الطالب');
                return;
            }

            // Verify student is in the list
            const student = examConfig.students.find(s => s.id === studentId);
            if (!student) {
                alert('رقم الطالب غير موجود في قائمة الطلاب المسجلين');
                return;
            }

            // Check if student already took the exam
            if (examResults.find(r => r.studentId === studentId)) {
                alert('لقد قمت بأداء هذا الامتحان مسبقاً');
                return;
            }

            // Set current student
            currentStudent = student;
            currentQuestionIndex = 0;
            studentAnswers = new Array(examConfig.questions.length).fill(null);

            // Shuffle questions for this student
            const shuffledQuestions = shuffleQuestionOptions(examConfig.questions);
            currentStudent.questions = shuffledQuestions;

            // Setup exam interface
            document.getElementById('studentLogin').style.display = 'none';
            document.getElementById('examInterface').style.display = 'block';

            // Set exam header
            document.getElementById('examHeader').textContent =
                `${examConfig.examTitle} - ${examConfig.courseName}`;

            // Start timer
            startExamTimer();

            // Display first question
            displayQuestion();
        }

        // ===== COUNTDOWN TIMER IMPLEMENTATION =====

        // Start exam countdown timer
        function startExamTimer() {
            timeRemaining = examConfig.duration * 60; // Convert minutes to seconds

            examTimer = setInterval(() => {
                timeRemaining--;
                updateTimerDisplay();

                // Warning when 5 minutes remaining
                if (timeRemaining <= 300 && timeRemaining > 0) {
                    document.getElementById('examTimer').classList.add('warning');
                }

                // Auto-submit when time expires
                if (timeRemaining <= 0) {
                    clearInterval(examTimer);
                    autoSubmitExam();
                }
            }, 1000);

            updateTimerDisplay();
        }

        // Update timer display
        function updateTimerDisplay() {
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timerDisplay').textContent = display;
        }

        // Auto-submit exam when timer expires
        function autoSubmitExam() {
            alert('انتهى وقت الامتحان. سيتم تسليم الامتحان تلقائياً.');

            // Disable all interactive elements
            disableExamInterface();

            // Submit exam
            processExamSubmission();
        }

        // Disable exam interface (critical security feature)
        function disableExamInterface() {
            // Disable all radio buttons
            const radioButtons = document.querySelectorAll('#currentQuestion input[type="radio"]');
            radioButtons.forEach(radio => radio.disabled = true);

            // Disable navigation buttons
            document.getElementById('prevBtn').disabled = true;
            document.getElementById('nextBtn').disabled = true;

            // Disable submit button
            const submitBtn = document.querySelector('button[onclick="submitExam()"]');
            if (submitBtn) submitBtn.disabled = true;

            // Add visual indication
            document.getElementById('examInterface').classList.add('disabled');
        }

        // ===== QUESTION DISPLAY AND NAVIGATION =====

        // Display current question
        function displayQuestion() {
            const question = currentStudent.questions[currentQuestionIndex];
            const questionDiv = document.getElementById('currentQuestion');

            let html = `
                <h3>السؤال ${currentQuestionIndex + 1} من ${examConfig.questions.length}</h3>
                <p style="font-size: 1.1rem; margin: 20px 0; line-height: 1.6;">${question.text}</p>
                <div style="margin: 20px 0;">
            `;

            // Display options (shuffled if enabled)
            const options = question.shuffledOptions || question.options;
            const optionLabels = ['أ', 'ب', 'ج', 'د'];

            options.forEach((option, index) => {
                const isChecked = studentAnswers[currentQuestionIndex] === index ? 'checked' : '';
                html += `
                    <div class="answer-option">
                        <input type="radio" name="currentAnswer" value="${index}" ${isChecked}
                               onchange="saveAnswer(${index})">
                        <label style="cursor: pointer; flex: 1;">${optionLabels[index]}. ${option}</label>
                    </div>
                `;
            });

            html += '</div>';
            questionDiv.innerHTML = html;

            // Update question counter
            document.getElementById('questionCounter').textContent =
                `السؤال ${currentQuestionIndex + 1} من ${examConfig.questions.length}`;

            // Update navigation buttons
            document.getElementById('prevBtn').style.display = currentQuestionIndex > 0 ? 'inline-block' : 'none';
            document.getElementById('nextBtn').textContent =
                currentQuestionIndex < examConfig.questions.length - 1 ? 'التالي' : 'الانتهاء';
        }

        // Save student answer
        function saveAnswer(answerIndex) {
            studentAnswers[currentQuestionIndex] = answerIndex;
        }

        // Navigate to previous question
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                displayQuestion();
            }
        }

        // Navigate to next question
        function nextQuestion() {
            if (currentQuestionIndex < examConfig.questions.length - 1) {
                currentQuestionIndex++;
                displayQuestion();
            } else {
                // Last question - show submit confirmation
                if (confirm('هل أنت متأكد من تسليم الامتحان؟ لن تتمكن من التعديل بعد التسليم.')) {
                    submitExam();
                }
            }
        }

        // Submit exam manually
        function submitExam() {
            if (confirm('هل أنت متأكد من تسليم الامتحان؟')) {
                clearInterval(examTimer);
                disableExamInterface();
                processExamSubmission();
            }
        }

        // ===== AUTO-GRADING SYSTEM =====

        // Process exam submission and calculate grade
        function processExamSubmission() {
            const submissionTime = new Date().toLocaleString('ar-SA');
            let correctAnswers = 0;

            // Calculate score by comparing student answers with correct answers
            for (let i = 0; i < examConfig.questions.length; i++) {
                const question = currentStudent.questions[i];
                const studentAnswer = studentAnswers[i];

                // Get the correct answer index for shuffled options
                const correctAnswerIndex = question.shuffledCorrectAnswer !== undefined
                    ? question.shuffledCorrectAnswer
                    : question.correctAnswer;

                if (studentAnswer === correctAnswerIndex) {
                    correctAnswers++;
                }
            }

            // Calculate percentage
            const percentage = Math.round((correctAnswers / examConfig.questions.length) * 100);

            // Create result object
            const result = {
                studentId: currentStudent.id,
                studentName: currentStudent.name,
                score: correctAnswers,
                totalQuestions: examConfig.questions.length,
                percentage: percentage,
                submissionTime: submissionTime,
                answers: [...studentAnswers]
            };

            // Add to results array
            examResults.push(result);

            // Show completion message
            alert(`تم تسليم الامتحان بنجاح!\nدرجتك: ${correctAnswers}/${examConfig.questions.length} (${percentage}%)`);

            // Reset student exam view
            resetStudentExamView();
        }

        // Reset student exam view for next student
        function resetStudentExamView() {
            document.getElementById('studentExamView').classList.remove('active');
            document.getElementById('studentLogin').style.display = 'block';
            document.getElementById('examInterface').style.display = 'none';
            document.getElementById('examInterface').classList.remove('disabled');
            document.getElementById('studentIdLogin').value = '';
            document.getElementById('examTimer').classList.remove('warning');

            // Re-enable interface elements
            const radioButtons = document.querySelectorAll('#currentQuestion input[type="radio"]');
            radioButtons.forEach(radio => radio.disabled = false);
            document.getElementById('prevBtn').disabled = false;
            document.getElementById('nextBtn').disabled = false;

            currentStudent = null;
            currentQuestionIndex = 0;
            studentAnswers = [];
        }

        // ===== RESULTS VIEW AND STATISTICS =====

        // Show teacher results view
        function showResultsView() {
            document.getElementById('examLinkDisplay').style.display = 'none';
            document.getElementById('teacherResultsView').classList.add('active');
            updateResultsDisplay();
        }

        // Update results display
        function updateResultsDisplay() {
            updateStatistics();
            updateResultsTable();
        }

        // Update statistics
        function updateStatistics() {
            const totalStudents = examResults.length;
            let totalPercentage = 0;

            examResults.forEach(result => {
                totalPercentage += result.percentage;
            });

            const averageScore = totalStudents > 0 ? Math.round(totalPercentage / totalStudents) : 0;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('averageScore').textContent = averageScore + '%';
        }

        // Update results table
        function updateResultsTable() {
            const tbody = document.getElementById('resultsTableBody');

            if (examResults.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #7f8c8d;">لا توجد نتائج بعد</td></tr>';
                return;
            }

            let html = '';
            examResults.forEach(result => {
                const scoreClass = getScoreClass(result.percentage);
                html += `
                    <tr>
                        <td>${result.studentId}</td>
                        <td>${result.studentName}</td>
                        <td>${result.score}/${result.totalQuestions}</td>
                        <td><span class="score-badge ${scoreClass}">${result.percentage}%</span></td>
                        <td>${result.submissionTime}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // Get score class for styling
        function getScoreClass(percentage) {
            if (percentage >= 85) return 'score-excellent';
            if (percentage >= 70) return 'score-good';
            return 'score-poor';
        }

        // ===== VIEW MANAGEMENT =====

        // Show teacher setup view
        function showTeacherSetup() {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.remove('active');
            });

            // Hide exam link display
            document.getElementById('examLinkDisplay').style.display = 'none';

            // Show teacher setup
            document.getElementById('teacherSetupView').classList.add('active');

            // Reset exam configuration for new exam
            resetExamConfiguration();
        }

        // Reset exam configuration
        function resetExamConfiguration() {
            // Clear form fields
            document.getElementById('collegeSelect').value = '';
            document.getElementById('majorSelect').value = '';
            document.getElementById('majorSelect').disabled = true;
            document.getElementById('courseName').value = '';
            document.getElementById('examTitle').value = '';
            document.getElementById('examDate').value = '';
            document.getElementById('examDuration').value = '';
            document.getElementById('shuffleAnswers').checked = false;
            document.getElementById('studentIdInput').value = '';
            document.getElementById('studentNameInput').value = '';

            // Reset data
            examConfig = {
                college: '',
                major: '',
                courseName: '',
                examTitle: '',
                examDate: '',
                duration: 0,
                students: [],
                questions: [],
                shuffleAnswers: false
            };

            examResults = [];

            // Clear displays
            updateStudentList();
            document.getElementById('questionsList').innerHTML = '';
        }

        // ===== UTILITY FUNCTIONS =====

        // Format time for display
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Generate random ID
        function generateRandomId() {
            return Math.random().toString(36).substr(2, 9);
        }

        // Validate Arabic text
        function isArabicText(text) {
            const arabicRegex = /[\u0600-\u06FF]/;
            return arabicRegex.test(text);
        }

        // ===== KEYBOARD SHORTCUTS =====

        // Add keyboard shortcuts for better user experience
        document.addEventListener('keydown', function(event) {
            // Only apply shortcuts in exam interface
            if (!document.getElementById('examInterface').style.display === 'block') return;

            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    nextQuestion();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    previousQuestion();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                    event.preventDefault();
                    const answerIndex = parseInt(event.key) - 1;
                    const radioButton = document.querySelector(`input[name="currentAnswer"][value="${answerIndex}"]`);
                    if (radioButton && !radioButton.disabled) {
                        radioButton.checked = true;
                        saveAnswer(answerIndex);
                    }
                    break;
            }
        });

        // ===== INITIALIZATION COMPLETE =====

        console.log('SUST Online Exam Platform initialized successfully');
        console.log('Developed for Sudan University of Science and Technology');
    </script>
</body>
</html>
