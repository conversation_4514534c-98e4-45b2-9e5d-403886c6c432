<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Database Systems Midterm - Online Exam Platform</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fonlineexa5436back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.6"></script>
</head>
<body class="bg-background min-h-screen">
    <!-- Sticky Header with Timer -->
    <header class="bg-surface shadow-subtle border-b border-light sticky top-0 z-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Exam Info -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L3 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-13-5z"/>
                            <path d="M14 14h4v2h-4v-2zm0-4h4v2h-4v-2zm0 8h4v2h-4v-2z" fill="white"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-lg font-semibold text-text-primary">Database Systems</h1>
                        <p class="text-sm text-text-secondary">Midterm Exam - Chapter 1-5</p>
                    </div>
                </div>

                <!-- Timer Display -->
                <div class="flex items-center space-x-4">
                    <div id="timerDisplay" class="bg-primary-50 border border-primary-200 rounded-lg px-4 py-2">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-clock text-primary"></i>
                            <span id="timeRemaining" class="text-lg font-semibold text-primary">01:29:45</span>
                        </div>
                        <p class="text-xs text-text-secondary text-center">Time Remaining</p>
                    </div>

                    <!-- Language Toggle -->
                    <button id="languageToggle" class="text-text-secondary hover:text-primary transition-colors">
                        <i class="fas fa-globe mr-1"></i>
                        <span id="currentLang">EN</span>
                    </button>
                </div>
            </div>

            <!-- Progress Indicator -->
            <div class="pb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-text-secondary">Question Progress</span>
                    <span id="questionProgress" class="text-sm font-medium text-text-primary">Question 3 of 20</span>
                </div>
                <div class="w-full bg-secondary-200 rounded-full h-2">
                    <div id="progressBar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 15%"></div>
                </div>
                <div class="flex justify-between mt-1 text-xs text-text-secondary">
                    <span>Answered: <span id="answeredCount" class="text-success-600 font-medium">2</span></span>
                    <span>Remaining: <span id="remainingCount" class="text-warning-600 font-medium">18</span></span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-surface rounded-lg shadow-subtle border border-light p-8">
            <!-- Question Display -->
            <div id="questionContainer" class="mb-8">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="bg-primary-50 p-2 rounded-lg">
                            <i class="fas fa-question-circle text-primary text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-text-primary">Question 3</h2>
                            <p class="text-sm text-text-secondary">Multiple Choice</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span id="questionStatus" class="px-3 py-1 rounded-full text-xs font-medium bg-warning-50 text-warning-700 border border-warning-200">
                            <i class="fas fa-clock mr-1"></i>Unanswered
                        </span>
                    </div>
                </div>

                <!-- Question Text -->
                <div class="mb-8">
                    <div class="bg-secondary-50 rounded-lg p-6 border border-light">
                        <p id="questionText" class="text-lg text-text-primary leading-relaxed">
                            Which of the following SQL commands is used to modify existing data in a database table?
                        </p>
                    </div>
                </div>

                <!-- Answer Options -->
                <div id="answerOptions" class="space-y-4">
                    <label class="flex items-start space-x-4 p-4 border border-light rounded-lg hover:bg-secondary-50 cursor-pointer transition-colors group">
                        <input type="radio" name="answer" value="A" class="mt-1 text-primary focus:ring-primary-500 focus:ring-2" />
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-semibold text-text-primary bg-secondary-100 px-2 py-1 rounded">A</span>
                            </div>
                            <p class="text-text-primary group-hover:text-text-primary">SELECT</p>
                        </div>
                    </label>

                    <label class="flex items-start space-x-4 p-4 border border-light rounded-lg hover:bg-secondary-50 cursor-pointer transition-colors group">
                        <input type="radio" name="answer" value="B" class="mt-1 text-primary focus:ring-primary-500 focus:ring-2" />
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-semibold text-text-primary bg-secondary-100 px-2 py-1 rounded">B</span>
                            </div>
                            <p class="text-text-primary group-hover:text-text-primary">INSERT</p>
                        </div>
                    </label>

                    <label class="flex items-start space-x-4 p-4 border border-light rounded-lg hover:bg-secondary-50 cursor-pointer transition-colors group">
                        <input type="radio" name="answer" value="C" class="mt-1 text-primary focus:ring-primary-500 focus:ring-2" />
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-semibold text-text-primary bg-secondary-100 px-2 py-1 rounded">C</span>
                            </div>
                            <p class="text-text-primary group-hover:text-text-primary">UPDATE</p>
                        </div>
                    </label>

                    <label class="flex items-start space-x-4 p-4 border border-light rounded-lg hover:bg-secondary-50 cursor-pointer transition-colors group">
                        <input type="radio" name="answer" value="D" class="mt-1 text-primary focus:ring-primary-500 focus:ring-2" />
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-semibold text-text-primary bg-secondary-100 px-2 py-1 rounded">D</span>
                            </div>
                            <p class="text-text-primary group-hover:text-text-primary">DELETE</p>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Navigation Controls -->
            <div class="flex items-center justify-between pt-6 border-t border-light">
                <button id="prevBtn" class="btn-secondary flex items-center space-x-2 px-6 py-3">
                    <i class="fas fa-chevron-left"></i>
                    <span>Previous</span>
                </button>

                <div class="flex items-center space-x-4">
                    <button id="markForReviewBtn" class="text-text-secondary hover:text-accent transition-colors flex items-center space-x-2">
                        <i class="fas fa-bookmark"></i>
                        <span class="text-sm">Mark for Review</span>
                    </button>
                    
                    <button id="clearAnswerBtn" class="text-text-secondary hover:text-warning transition-colors flex items-center space-x-2">
                        <i class="fas fa-eraser"></i>
                        <span class="text-sm">Clear Answer</span>
                    </button>
                </div>

                <div class="flex items-center space-x-3">
                    <button id="nextBtn" class="btn-primary flex items-center space-x-2 px-6 py-3">
                        <span>Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    
                    <button id="submitBtn" class="btn-primary bg-success hover:bg-success-600 flex items-center space-x-2 px-6 py-3 hidden">
                        <i class="fas fa-check-circle"></i>
                        <span>Submit Exam</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Question Navigation Panel (Mobile Hidden) -->
        <div class="hidden lg:block mt-8">
            <div class="bg-surface rounded-lg shadow-subtle border border-light p-6">
                <h3 class="text-lg font-medium text-text-primary mb-4">Question Navigation</h3>
                <div id="questionNavGrid" class="grid grid-cols-10 gap-2">
                    <!-- Question navigation buttons will be generated here -->
                </div>
                <div class="flex items-center justify-center space-x-6 mt-4 text-sm">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-success rounded"></div>
                        <span class="text-text-secondary">Answered</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-warning rounded"></div>
                        <span class="text-text-secondary">Marked</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-secondary-300 rounded"></div>
                        <span class="text-text-secondary">Unanswered</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-primary rounded"></div>
                        <span class="text-text-secondary">Current</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Timer Warning Modal -->
    <div id="timerWarningModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-md w-full mx-4 p-6">
            <div class="text-center">
                <div id="warningIcon" class="mx-auto w-16 h-16 bg-warning-50 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-exclamation-triangle text-warning text-2xl"></i>
                </div>
                <h3 id="warningTitle" class="text-lg font-medium text-text-primary mb-2">Time Warning</h3>
                <p id="warningMessage" class="text-text-secondary mb-6">You have 10 minutes remaining to complete the exam.</p>
                <button id="continueBtn" class="btn-primary w-full">Continue Exam</button>
            </div>
        </div>
    </div>

    <!-- Auto-Submit Modal -->
    <div id="autoSubmitModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-md w-full mx-4 p-6">
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-error-50 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-clock text-error text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-text-primary mb-2">Time Expired</h3>
                <p class="text-text-secondary mb-6">Your exam has been automatically submitted as the time limit has been reached.</p>
                <div class="space-y-3">
                    <button id="viewResultsBtn" class="btn-primary w-full">View Results</button>
                    <a href="student_exam_login.html" class="btn-secondary w-full text-center block">Return to Login</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Confirmation Modal -->
    <div id="submitConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-surface rounded-lg shadow-modal max-w-md w-full mx-4 p-6">
            <div class="text-center">
                <div class="mx-auto w-16 h-16 bg-warning-50 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-question-circle text-warning text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-text-primary mb-2">Submit Exam?</h3>
                <p class="text-text-secondary mb-2">Are you sure you want to submit your exam?</p>
                <div class="bg-secondary-50 rounded-lg p-4 mb-6">
                    <div class="text-sm space-y-1">
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Answered:</span>
                            <span class="text-success-600 font-medium">15 of 20</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Marked for Review:</span>
                            <span class="text-warning-600 font-medium">2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-text-secondary">Unanswered:</span>
                            <span class="text-error-600 font-medium">3</span>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button id="cancelSubmitBtn" class="btn-secondary flex-1">Cancel</button>
                    <button id="confirmSubmitBtn" class="btn-primary bg-success hover:bg-success-600 flex-1">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Exam state management
        let examState = {
            currentQuestion: 3,
            totalQuestions: 20,
            timeRemaining: 5385, // 89 minutes 45 seconds in seconds
            answers: {},
            markedForReview: new Set(),
            examStarted: true,
            examEnded: false
        };

        // Sample questions data
        const questions = [
            {
                id: 1,
                text: "What is the primary key in a relational database?",
                options: [
                    { value: "A", text: "A unique identifier for each record" },
                    { value: "B", text: "A foreign key reference" },
                    { value: "C", text: "An index for faster queries" },
                    { value: "D", text: "A data validation rule" }
                ],
                correctAnswer: "A"
            },
            {
                id: 2,
                text: "Which SQL command is used to retrieve data from a database?",
                options: [
                    { value: "A", text: "SELECT" },
                    { value: "B", text: "INSERT" },
                    { value: "C", text: "UPDATE" },
                    { value: "D", text: "DELETE" }
                ],
                correctAnswer: "A"
            },
            {
                id: 3,
                text: "Which of the following SQL commands is used to modify existing data in a database table?",
                options: [
                    { value: "A", text: "SELECT" },
                    { value: "B", text: "INSERT" },
                    { value: "C", text: "UPDATE" },
                    { value: "D", text: "DELETE" }
                ],
                correctAnswer: "C"
            }
            // Add more questions as needed
        ];

        // Initialize exam
        document.addEventListener('DOMContentLoaded', function() {
            initializeExam();
            startTimer();
            generateQuestionNavigation();
            updateDisplay();
        });

        function initializeExam() {
            // Set initial answers (some pre-answered for demo)
            examState.answers[1] = "A";
            examState.answers[2] = "A";
            examState.markedForReview.add(2);
        }

        function startTimer() {
            const timerInterval = setInterval(() => {
                if (examState.examEnded) {
                    clearInterval(timerInterval);
                    return;
                }

                examState.timeRemaining--;
                updateTimerDisplay();

                // Timer warnings
                if (examState.timeRemaining === 600) { // 10 minutes
                    showTimerWarning("10 minutes remaining", "warning");
                } else if (examState.timeRemaining === 300) { // 5 minutes
                    showTimerWarning("5 minutes remaining", "error");
                } else if (examState.timeRemaining === 60) { // 1 minute
                    showTimerWarning("1 minute remaining", "error");
                } else if (examState.timeRemaining <= 0) {
                    autoSubmitExam();
                    clearInterval(timerInterval);
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const hours = Math.floor(examState.timeRemaining / 3600);
            const minutes = Math.floor((examState.timeRemaining % 3600) / 60);
            const seconds = examState.timeRemaining % 60;
            
            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timeRemaining').textContent = timeString;

            // Change timer color based on remaining time
            const timerDisplay = document.getElementById('timerDisplay');
            if (examState.timeRemaining <= 300) { // 5 minutes
                timerDisplay.className = 'bg-error-50 border border-error-200 rounded-lg px-4 py-2';
                document.getElementById('timeRemaining').className = 'text-lg font-semibold text-error';
            } else if (examState.timeRemaining <= 600) { // 10 minutes
                timerDisplay.className = 'bg-warning-50 border border-warning-200 rounded-lg px-4 py-2';
                document.getElementById('timeRemaining').className = 'text-lg font-semibold text-warning';
            }
        }

        function showTimerWarning(message, type) {
            const modal = document.getElementById('timerWarningModal');
            const icon = document.getElementById('warningIcon');
            const title = document.getElementById('warningTitle');
            const messageEl = document.getElementById('warningMessage');

            if (type === 'error') {
                icon.className = 'mx-auto w-16 h-16 bg-error-50 rounded-full flex items-center justify-center mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-triangle text-error text-2xl"></i>';
            }

            title.textContent = 'Time Warning';
            messageEl.textContent = message;
            
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        function updateDisplay() {
            // Update question progress
            document.getElementById('questionProgress').textContent = `Question ${examState.currentQuestion} of ${examState.totalQuestions}`;
            
            // Update progress bar
            const progressPercentage = (examState.currentQuestion / examState.totalQuestions) * 100;
            document.getElementById('progressBar').style.width = `${progressPercentage}%`;

            // Update answered/remaining counts
            const answeredCount = Object.keys(examState.answers).length;
            document.getElementById('answeredCount').textContent = answeredCount;
            document.getElementById('remainingCount').textContent = examState.totalQuestions - answeredCount;

            // Load current question
            loadQuestion(examState.currentQuestion);

            // Update navigation buttons
            updateNavigationButtons();

            // Update question status
            updateQuestionStatus();
        }

        function loadQuestion(questionNumber) {
            const question = questions.find(q => q.id === questionNumber);
            if (!question) return;

            document.getElementById('questionText').textContent = question.text;
            
            // Shuffle options (Fisher-Yates algorithm)
            const shuffledOptions = [...question.options];
            for (let i = shuffledOptions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffledOptions[i], shuffledOptions[j]] = [shuffledOptions[j], shuffledOptions[i]];
            }

            // Update answer options
            const optionsContainer = document.getElementById('answerOptions');
            optionsContainer.innerHTML = '';

            shuffledOptions.forEach(option => {
                const optionHTML = `
                    <label class="flex items-start space-x-4 p-4 border border-light rounded-lg hover:bg-secondary-50 cursor-pointer transition-colors group">
                        <input type="radio" name="answer" value="${option.value}" class="mt-1 text-primary focus:ring-primary-500 focus:ring-2">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-semibold text-text-primary bg-secondary-100 px-2 py-1 rounded">${option.value}</span>
                            </div>
                            <p class="text-text-primary group-hover:text-text-primary">${option.text}</p>
                        </div>
                    </label>
                `;
                optionsContainer.insertAdjacentHTML('beforeend', optionHTML);
            });

            // Set previously selected answer
            if (examState.answers[questionNumber]) {
                const selectedOption = document.querySelector(`input[value="${examState.answers[questionNumber]}"]`);
                if (selectedOption) {
                    selectedOption.checked = true;
                }
            }

            // Add event listeners for answer selection
            document.querySelectorAll('input[name="answer"]').forEach(input => {
                input.addEventListener('change', function() {
                    examState.answers[examState.currentQuestion] = this.value;
                    updateDisplay();
                });
            });
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            // Previous button
            prevBtn.disabled = examState.currentQuestion === 1;
            prevBtn.className = examState.currentQuestion === 1 
                ? 'btn-secondary opacity-50 cursor-not-allowed flex items-center space-x-2 px-6 py-3'
                : 'btn-secondary flex items-center space-x-2 px-6 py-3';

            // Next/Submit button
            if (examState.currentQuestion === examState.totalQuestions) {
                nextBtn.classList.add('hidden');
                submitBtn.classList.remove('hidden');
            } else {
                nextBtn.classList.remove('hidden');
                submitBtn.classList.add('hidden');
            }
        }

        function updateQuestionStatus() {
            const statusEl = document.getElementById('questionStatus');
            const currentQ = examState.currentQuestion;

            if (examState.answers[currentQ]) {
                statusEl.className = 'px-3 py-1 rounded-full text-xs font-medium bg-success-50 text-success-700 border border-success-200';
                statusEl.innerHTML = '<i class="fas fa-check mr-1"></i>Answered';
            } else if (examState.markedForReview.has(currentQ)) {
                statusEl.className = 'px-3 py-1 rounded-full text-xs font-medium bg-warning-50 text-warning-700 border border-warning-200';
                statusEl.innerHTML = '<i class="fas fa-bookmark mr-1"></i>Marked';
            } else {
                statusEl.className = 'px-3 py-1 rounded-full text-xs font-medium bg-warning-50 text-warning-700 border border-warning-200';
                statusEl.innerHTML = '<i class="fas fa-clock mr-1"></i>Unanswered';
            }
        }

        function generateQuestionNavigation() {
            const navGrid = document.getElementById('questionNavGrid');
            navGrid.innerHTML = '';

            for (let i = 1; i <= examState.totalQuestions; i++) {
                let buttonClass = 'w-8 h-8 rounded text-sm font-medium transition-colors ';
                
                if (i === examState.currentQuestion) {
                    buttonClass += 'bg-primary text-white';
                } else if (examState.answers[i]) {
                    buttonClass += 'bg-success text-white';
                } else if (examState.markedForReview.has(i)) {
                    buttonClass += 'bg-warning text-white';
                } else {
                    buttonClass += 'bg-secondary-300 text-text-secondary hover:bg-secondary-400';
                }

                const button = document.createElement('button');
                button.className = buttonClass;
                button.textContent = i;
                button.addEventListener('click', () => {
                    examState.currentQuestion = i;
                    updateDisplay();
                });

                navGrid.appendChild(button);
            }
        }

        // Event listeners
        document.getElementById('prevBtn').addEventListener('click', function() {
            if (examState.currentQuestion > 1) {
                examState.currentQuestion--;
                updateDisplay();
            }
        });

        document.getElementById('nextBtn').addEventListener('click', function() {
            if (examState.currentQuestion < examState.totalQuestions) {
                examState.currentQuestion++;
                updateDisplay();
            }
        });

        document.getElementById('markForReviewBtn').addEventListener('click', function() {
            const currentQ = examState.currentQuestion;
            if (examState.markedForReview.has(currentQ)) {
                examState.markedForReview.delete(currentQ);
                this.innerHTML = '<i class="fas fa-bookmark"></i><span class="text-sm">Mark for Review</span>';
            } else {
                examState.markedForReview.add(currentQ);
                this.innerHTML = '<i class="fas fa-bookmark text-warning"></i><span class="text-sm text-warning">Marked</span>';
            }
            updateDisplay();
        });

        document.getElementById('clearAnswerBtn').addEventListener('click', function() {
            delete examState.answers[examState.currentQuestion];
            document.querySelectorAll('input[name="answer"]').forEach(input => {
                input.checked = false;
            });
            updateDisplay();
        });

        document.getElementById('submitBtn').addEventListener('click', function() {
            showSubmitConfirmation();
        });

        document.getElementById('continueBtn').addEventListener('click', function() {
            document.getElementById('timerWarningModal').classList.add('hidden');
            document.getElementById('timerWarningModal').classList.remove('flex');
        });

        document.getElementById('cancelSubmitBtn').addEventListener('click', function() {
            document.getElementById('submitConfirmModal').classList.add('hidden');
            document.getElementById('submitConfirmModal').classList.remove('flex');
        });

        document.getElementById('confirmSubmitBtn').addEventListener('click', function() {
            submitExam();
        });

        document.getElementById('viewResultsBtn').addEventListener('click', function() {
            window.location.href = 'teacher_results_dashboard.html';
        });

        // Language toggle
        document.getElementById('languageToggle').addEventListener('click', function() {
            const currentLang = document.getElementById('currentLang');
            const isEnglish = currentLang.textContent === 'EN';
            currentLang.textContent = isEnglish ? 'AR' : 'EN';
            document.documentElement.dir = isEnglish ? 'rtl' : 'ltr';
            document.documentElement.lang = isEnglish ? 'ar' : 'en';
        });

        function showSubmitConfirmation() {
            const modal = document.getElementById('submitConfirmModal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        function submitExam() {
            examState.examEnded = true;
            
            // Hide submit confirmation modal
            document.getElementById('submitConfirmModal').classList.add('hidden');
            document.getElementById('submitConfirmModal').classList.remove('flex');
            
            // Show success message and redirect
            setTimeout(() => {
                window.location.href = 'teacher_results_dashboard.html';
            }, 1000);
        }

        function autoSubmitExam() {
            examState.examEnded = true;
            
            // Disable all form elements
            document.querySelectorAll('input, button').forEach(element => {
                element.disabled = true;
            });
            
            // Show auto-submit modal
            const modal = document.getElementById('autoSubmitModal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        // Prevent page refresh/navigation during exam
        window.addEventListener('beforeunload', function(e) {
            if (examState.examStarted && !examState.examEnded) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Disable common keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && (e.key === 'r' || e.key === 'R' || e.key === 'u' || e.key === 'U' || e.key === 's' || e.key === 'S')) {
                e.preventDefault();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.preventDefault();
            }
        });
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>